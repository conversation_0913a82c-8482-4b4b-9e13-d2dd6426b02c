import { Skeleton, cn } from "@heroui/react";
import PropTypes from "prop-types";
import { <PERSON>, <PERSON>, <PERSON><PERSON>, ResponsiveContainer } from "recharts";

const COLORS = [
  "#D32F2F",
  "#E53935",
  "#FB8C00",
  "#FFA726",
  "#FFCA28",
  "#FFEE58",
  "#9CCC65",
  "#8BC34A",
  "#689F38",
  "#4CAF50",
];

const GREY_COLOR = "#E0E0E0";

const renderLabel = ({
  cx,
  cy,
  midAngle,
  innerRadius,
  outerRadius,
  index,
  lastUserLevel,
  allLevels,
}) => {
  const lastUserLevelIndex = allLevels.indexOf(lastUserLevel);
  if (index === lastUserLevelIndex && lastUserLevelIndex !== -1) {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) / 2;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);
    return (
      <text
        x={x}
        y={y + 3}
        fill="#FFFFFF"
        className="pointer-events-none select-none text-base font-medium"
        textAnchor="middle"
        dominantBaseline="central"
      >
        {lastUserLevel}
      </text>
    );
  }
  return null;
};

const CircularChartCard = ({
  className,
  isLoading,
  lastUserLevel,
  allLevels = [],
}) => {
  const totalSegments = allLevels.length;
  const lastUserLevelIndex = allLevels.indexOf(lastUserLevel);
  const completedSegmentsCount =
    lastUserLevelIndex !== -1 ? lastUserLevelIndex + 1 : 0;
  const progressPercentage = (completedSegmentsCount / totalSegments) * 100;

  const data = Array.from({ length: totalSegments }, (_, index) => {
    const isCompleted = index < completedSegmentsCount;
    const fill = isCompleted ? COLORS[index % COLORS.length] : GREY_COLOR;
    return {
      name: `بخش ${index + 1}`,
      value: 1,
      fill,
    };
  });

  return (
    <div
      className={cn(
        "relative flex flex-col transition-all justify-between rounded-small bg-background pt-5 pb-4 px-4 items-center",
        className,
      )}
    >
      {!isLoading ? (
        <p className="mb-2 font-medium">درصد پیشرفت</p>
      ) : (
        <Skeleton className="h-4 max-w-xs w-full rounded-lg" />
      )}

      <div className="relative w-full h-full aspect-square max-w-[300px] max-h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              cornerRadius={20}
              innerRadius="50%"
              outerRadius="70%"
              paddingAngle={5}
              dataKey="value"
              startAngle={90}
              endAngle={90 - 360}
              data={data}
              label={({ ...props }) =>
                renderLabel({ ...props, lastUserLevel, allLevels })
              }
              labelLine={false}
              cx="50%"
              cy="50%"
              blendStroke
              className="focus:outline-none"
              animationDuration={600}
              animationBegin={0}
              animationEasing="ease-out"
            >
              {data.map((entry) => (
                <Cell key={entry.name} fill={entry.fill} />
              ))}
            </Pie>
          </PieChart>
        </ResponsiveContainer>

        <div className="absolute inset-0 grid place-items-center text-4xl font-bold text-gray-700">
          {`${Math.round(progressPercentage)}%`}
        </div>
      </div>
    </div>
  );
};

CircularChartCard.propTypes = {
  className: PropTypes.string,
  isLoading: PropTypes.bool,
  lastUserLevel: PropTypes.string.isRequired,
  allLevels: PropTypes.arrayOf(PropTypes.string),
};

export default CircularChartCard;
