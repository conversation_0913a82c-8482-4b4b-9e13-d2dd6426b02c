import { DatePicker, DateRangePicker, cn } from "@heroui/react";
import { parseDate } from "@internationalized/date";
import PropTypes from "prop-types";
import { useController } from "react-hook-form";

/**
 * @param {FormDatePickerProps} props
 */
const FormDatePicker = (props) => {
  const {
    name,
    control,
    className,
    classNames = {},
    isRange,
    ...other
  } = props;

  const {
    field: { onChange, ...field },
    fieldState,
  } = useController({ name, control });

  const defaultProps = {
    labelPlacement: "outside",
    className: cn("z-0", className),
    ...other,
  };

  if (isRange) {
    const parsedValue = field.value
      ? {
          start: parseDate(field.value.split(",")[0]),
          end: parseDate(field.value.split(",")[1]),
        }
      : null;

    return (
      <DateRangePicker
        {...field}
        value={parsedValue}
        showMonthAndYearPickers
        onChange={(e) => {
          const startDate = e?.start
            ? `${e.start.year}-${e.start.month.toString().padStart(2, "0")}-${e.start.day.toString().padStart(2, "0")}`
            : null;
          const endDate = e?.end
            ? `${e.end.year}-${e.end.month.toString().padStart(2, "0")}-${e.end.day.toString().padStart(2, "0")}`
            : null;

          const rangeValue =
            startDate && endDate ? `${startDate},${endDate}` : null;
          onChange(rangeValue);
        }}
        isInvalid={!!fieldState.error}
        errorMessage={fieldState.error?.message}
        classNames={{
          segment: cn("ltr", classNames?.segment),
          input: cn("ltr justify-end", classNames?.input),
          inputWrapper: cn(
            [
              !fieldState.error ? "bg-background" : "",
              "hover:bg-background-100",
              "dark:hover:bg-background-200/90",
            ],
            classNames?.inputWrapper,
          ),
          ...(({ segment, input, inputWrapper, ...rest }) => rest)(classNames),
        }}
        {...defaultProps}
      />
    );
  }
  const parsedValue = field.value ? parseDate(field.value) : null;

  return (
    <DatePicker
      {...field}
      value={parsedValue}
      showMonthAndYearPickers
      classNames={{
        segment: cn("ltr", classNames?.segment),
        input: cn("ltr justify-end", classNames?.input),
        inputWrapper: cn(
          [
            !fieldState.error ? "bg-background" : "",
            "hover:bg-background-100",
            "dark:hover:bg-background-200/90",
          ],
          classNames?.inputWrapper,
        ),
        ...(({ segment, input, inputWrapper, ...rest }) => rest)(classNames),
      }}
      onChange={(e) => {
        const selectedDate = `${e.year}-${e.month.toString().padStart(2, "0")}-${e.day.toString().padStart(2, "0")}`;
        onChange(selectedDate);
      }}
      isInvalid={!!fieldState.error}
      errorMessage={fieldState.error?.message}
      {...defaultProps}
    />
  );
};

FormDatePicker.propTypes = {
  name: PropTypes.string.isRequired,
  control: PropTypes.object.isRequired,
  classNames: PropTypes.shape({
    segment: PropTypes.string,
    input: PropTypes.string,
    inputWrapper: PropTypes.string,
    base: PropTypes.string,
  }),
  isRange: PropTypes.bool,
  className: PropTypes.string,
  color: PropTypes.string,
  label: PropTypes.string,
  variant: PropTypes.oneOf(["faded", "bordered", "flat", "underline"]),
  labelPlacement: PropTypes.oneOf(["outside", "inside", "outside-left"]),
  radius: PropTypes.oneOf(["none", "sm", "md", "lg", "full"]),
  size: PropTypes.oneOf(["sm", "md", "lg"]),
};

export default FormDatePicker;
