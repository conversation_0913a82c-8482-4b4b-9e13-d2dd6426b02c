import { cn, Select, SelectItem } from '@heroui/react';
import PropTypes from 'prop-types';
import { useController } from 'react-hook-form';

const FormSelect = ({
  control,
  name,
  className,
  items = [],
  selectionMode = 'single',
  wrapperClassName,
  valueClassName,
  rules,

  ...other
}) => {
  const {
    field: { onChange, value, ...filed },
    fieldState,
  } = useController({
    name,
    control,
    rules: rules,
  });

  const defaultProps = {
    labelPlacement: 'outside',
    placeholder: ' ',
    className: cn('w-full z-0', className),
    selectionMode,
    selectedKeys: value ? new Set(value?.split(',')) : new Set([]),
    ...other,
  };

  const handleSelectionChange = (keys) => {
    const selectedValues = Array.from(keys ? new Set(keys) : new Set([]));
    onChange(
      selectionMode === 'multiple'
        ? selectedValues.join(',')
        : selectedValues[0],
    );
  };

  return (
    <Select
      {...filed}
      value={selectionMode === 'multiple' ? value?.split(',') : value}
      onSelectionChange={handleSelectionChange}
      isInvalid={!!fieldState.error}
      errorMessage={fieldState.error?.message}
      {...defaultProps}
      classNames={{
        trigger: [
          'hover:bg-background-50 dark:hover:bg-background-50',
          { 'bg-background-100': !fieldState.error },
          wrapperClassName,
        ],
        value: valueClassName,
      }}>
      {items?.map((item) => (
        <SelectItem
          classNames={{
            title: 'text-sm font-medium ',
          }}
          key={item.value}>
          {item.value}
        </SelectItem>
      ))}
    </Select>
  );
};

FormSelect.propTypes = {
  name: PropTypes.string.isRequired,
  control: PropTypes.object.isRequired,
  items: PropTypes.arrayOf(PropTypes.object),
  className: PropTypes.string,
  variant: PropTypes.oneOf(['faded', 'bordered', 'flat', 'underline']),
  labelPlacement: PropTypes.oneOf(['outside', 'inside', 'outside-left']),
  placeholder: PropTypes.string,
  color: PropTypes.string,
  label: PropTypes.string,
  selectionMode: PropTypes.oneOf(['single', 'multiple']),
  radius: PropTypes.oneOf(['none', 'sm', 'md', 'lg', 'full']),
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  wrapperClassName: PropTypes.string,
  valueClassName: PropTypes.string,
  rules: PropTypes.object,
};

export default FormSelect;
