import { <PERSON><PERSON>, <PERSON>, DatePicker, DateRangePicker, cn } from "@heroui/react";
import { parseDate } from "@internationalized/date";
import dayjs from "dayjs";
import { CloseCircle } from "iconsax-reactjs";
import jalaliday from "jalaliday";
import { useQueryState } from "nuqs";
import PropTypes from "prop-types";
import { useState } from "react";
import getCurrentDate from "../../../utils/getCurrentDate";

dayjs.extend(jalaliday);

/**
 * @param {DatePickerFilterProps} props
 */
const DateFilter = (props) => {
  const {
    value,
    setValue,
    name,
    isRange = false,
    classNames,
    className,
    ...other
  } = props;

  const [filter, setFilter] = useQueryState(name);
  const [validate, setValidate] = useState(false);

  const defaultProps = {
    labelPlacement: "outside",
    className: cn("z-0", className),
    ...other,
  };

  const handleOnChange = (e) => {
    if (isRange) {
      const startDate = e?.start
        ? `${e.start.year}-${e.start.month.toString().padStart(2, "0")}-${e.start.day.toString().padStart(2, "0")}`
        : null;
      const endDate = e?.end
        ? `${e.end.year}-${e.end.month.toString().padStart(2, "0")}-${e.end.day.toString().padStart(2, "0")}`
        : null;

      const rangeValue =
        startDate && endDate ? `${startDate},${endDate}` : null;

      if (setValue) {
        setValue(rangeValue);
      } else {
        setFilter(rangeValue);
      }
      setValidate(false);
    } else {
      const selectedDate = `${e.year}-${e.month.toString().padStart(2, "0")}-${e.day.toString().padStart(2, "0")}`;

      if (
        selectedDate.split("-").join("") < getCurrentDate().split("-").join("")
      ) {
        if (setValue) {
          setValue(selectedDate);
        } else {
          setFilter(selectedDate);
        }
        setValidate(false);
      } else {
        setValidate(true);
      }
    }
  };

  if (isRange) {
    const parsedValue = value
      ? {
          start: parseDate(value.split(",")[0]),
          end: parseDate(value.split(",")[1]),
        }
      : null;
    const parsedFilter = filter
      ? {
          start: parseDate(filter.split(",")[0]),
          end: parseDate(filter.split(",")[1]),
        }
      : null;

    return (
      <DateRangePicker
        showMonthAndYearPickers
        onChange={(e) => {
          handleOnChange(e);
        }}
        value={
          value
            ? parsedValue
            : value === ""
              ? null
              : filter
                ? parsedFilter
                : null
        }
        isInvalid={validate}
        errorMessage={(val) => {
          if (val.isInvalid) {
            return "تاریخ باید صحیح باشد";
          }
        }}
        classNames={{
          segment: cn("ltr", classNames?.segment),
          input: cn("ltr justify-end", classNames?.input),
          inputWrapper: cn(
            [
              !validate ? "bg-background" : "",
              "hover:bg-background-100",
              "dark:hover:bg-background-200/90",
            ],
            classNames?.inputWrapper,
          ),
          ...(({ segment, input, inputWrapper, ...rest }) => rest)(classNames),
        }}
        endContent={
          (filter || value) && (
            <Button
              variant="light"
              size="sm"
              radius="full"
              isIconOnly
              color="danger"
              className="-me-2"
              onPress={() => {
                handleOnChange(null);
              }}
            >
              <CloseCircle size={20} />
            </Button>
          )
        }
        {...defaultProps}
      />
    );
  }

  return (
    <DatePicker
      onChange={(e) => {
        handleOnChange(e);
      }}
      value={
        value
          ? parseDate(value)
          : value === ""
            ? null
            : filter
              ? parseDate(filter)
              : null
      }
      isInvalid={validate}
      errorMessage={(val) => {
        if (val.isInvalid) {
          return "تاریخ باید صحیح باشد";
        }
      }}
      classNames={{
        segment: cn("ltr", classNames?.segment),
        input: cn("ltr justify-end", classNames?.input),
        inputWrapper: cn(
          [
            !validate ? "bg-background" : "",
            "hover:bg-background-100",
            "dark:hover:bg-background-200/90",
          ],
          classNames?.inputWrapper,
        ),
        ...(({ segment, input, inputWrapper, ...rest }) => rest)(classNames),
      }}
      {...defaultProps}
    />
  );
};

DateFilter.propTypes = {
  name: PropTypes.string.isRequired,
  classNames: PropTypes.shape({
    segment: PropTypes.string,
    input: PropTypes.string,
    inputWrapper: PropTypes.string,
    base: PropTypes.string,
  }),
  isRange: PropTypes.bool,
  labelPlacement: PropTypes.oneOf(["outside", "inside", "outside-left"]),
  label: PropTypes.string,
  variant: PropTypes.oneOf(["faded", "bordered", "flat", "underline"]),
  radius: PropTypes.oneOf(["none", "sm", "md", "lg", "full"]),
  size: PropTypes.oneOf(["sm", "md", "lg"]),
  className: PropTypes.string,
  setValue: PropTypes.func,
  value: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.shape({
      start: PropTypes.string,
      end: PropTypes.string,
    }),
  ]),
  historyProps: PropTypes.shape({
    className: PropTypes.string,
    chipClassName: PropTypes.string,
    chipSize: PropTypes.oneOf(["sm", "md", "lg"]),
    chipRadius: PropTypes.oneOf(["none", "sm", "md", "lg", "full"]),
    chipColor: PropTypes.oneOf([
      "primary",
      "secondary",
      "success",
      "warning",
      "danger",
    ]),
    chipVariant: PropTypes.oneOf([
      "solid",
      "bordered",
      "light",
      "flat",
      "dot",
      "faded",
      "shadow",
    ]),
  }),
};

const History = (props) => {
  const { name, label, setValue, isRange, value, historyProps } = props;
  const [filter, setFilter] = useQueryState(name);

  const displayValue = value ? value : filter;

  if (
    !displayValue ||
    (typeof displayValue === "object" &&
      (!displayValue.start || !displayValue.end))
  ) {
    return <></>;
  }

  const formattedDate =
    typeof displayValue === "object"
      ? `${dayjs(displayValue.start).calendar("jalali").locale("fa").format("YYYY/MM/DD")} - ${dayjs(displayValue.end).calendar("jalali").locale("fa").format("YYYY/MM/DD")}`
      : dayjs(displayValue)
          .calendar("jalali")
          .locale("fa")
          .format("YYYY/MM/DD");

  return (
    <div
      className={cn(
        "flex w-max max-w-lg border-spacing-1 flex-wrap items-center gap-2 rounded-md border border-dashed border-foreground-100 px-1.5 py-2 font-medium",
        historyProps?.className,
      )}
    >
      {label && <p className="text-sm">{label}:</p>}
      <Chip
        radius={historyProps?.chipRadius ?? "sm"}
        size={historyProps?.chipSize ?? "sm"}
        color={historyProps?.chipColor}
        variant={historyProps?.chipVariant}
        className={cn("p-1.5 text-sm", historyProps?.chipClassName)}
        onClose={() => {
          if (setValue) {
            setValue(isRange ? null : "");
          } else {
            setFilter(isRange ? null : "");
          }
        }}
      >
        {formattedDate}
      </Chip>
    </div>
  );
};

History.propTypes = {
  label: PropTypes.string,
  name: PropTypes.string.isRequired,
  isRange: PropTypes.bool,
  setValue: PropTypes.func,
  value: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.shape({
      start: PropTypes.string,
      end: PropTypes.string,
    }),
  ]),
  historyProps: PropTypes.shape({
    className: PropTypes.string,
    chipClassName: PropTypes.string,
    chipSize: PropTypes.oneOf(["sm", "md", "lg"]),
    chipRadius: PropTypes.oneOf(["none", "sm", "md", "lg", "full"]),
    chipColor: PropTypes.oneOf([
      "primary",
      "secondary",
      "success",
      "warning",
      "danger",
    ]),
    chipVariant: PropTypes.oneOf([
      "solid",
      "bordered",
      "light",
      "flat",
      "dot",
      "faded",
      "shadow",
    ]),
  }),
};
DateFilter.History = History;
History.displayName = "date-filter-history";
DateFilter.showInDrawer = true;
export default DateFilter;
