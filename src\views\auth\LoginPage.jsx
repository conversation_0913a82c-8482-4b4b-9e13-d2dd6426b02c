import { useState } from "react";
import MobileStep from "./MobileStep";
import OtpStep from "./OtpStep";
import PasswordStep from "./PasswordStep";

const LoginPage = () => {
  const [step, setStep] = useState("mobile");
  const [authData, setAuthData] = useState({});

  document.title = document.title = "باران - ورود و ثبت نام";

  return (
    <div className="relative flex h-dvh w-full flex-col items-center justify-center overflow-hidden bg-background-100 px-4">
      {/* Pattern */}
      <img
        src="/images/patterns/baranPattern.png"
        alt="pattern"
        className="absolute -bottom-5 -left-5 z-0 w-1/6 min-w-40 rotate-12 opacity-15"
      />

      {/* Form */}
      <div className="relative z-10 flex w-full max-w-md flex-col items-center justify-center rounded-lg bg-background-50 bg-opacity-0 p-8 sm:bg-opacity-100 sm:p-10 sm:shadow-small md:p-12 lg:p-14">
        <img
          src="/images/patterns/baranPattern.png"
          alt="logo"
          className="mb-10 size-16"
        />

        <h1 className="mb-6 text-lg font-bold">ورود / ثبت نام</h1>

        {step === "mobile" && (
          <MobileStep
            onSuccess={(data) => {
              setAuthData(data);
              setStep(data.data?.has_password ? "password" : "otp");
            }}
          />
        )}

        {step === "password" && (
          <PasswordStep mobile={authData?.mobile} setStep={setStep} />
        )}

        {step === "otp" && (
          <OtpStep
            mobile={authData?.mobile}
            setStep={setStep}
            expireTime={authData?.data?.expire}
          />
        )}
      </div>
    </div>
  );
};

export default LoginPage;
