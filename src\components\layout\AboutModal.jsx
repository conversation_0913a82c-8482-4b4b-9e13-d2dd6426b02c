import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  useDisclosure,
} from "@heroui/react";
import { useQueryClient } from "@tanstack/react-query";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import ReactPlayer from "react-player";
import api from "../../api";
import FormCheckbox from "../form/FormCheckbox";

const AboutModal = () => {
  const {
    isOpen: isAboutOpen,
    onOpen: onAboutOpen,
    onClose: onAboutClose,
  } = useDisclosure();

  const { control, getValues } = useForm({
    defaultValues: {
      isSeen: false,
    },
  });

  const queryClient = useQueryClient();
  const cachedMeData = queryClient.getQueryData(api.Auth.me.getKey());
  const video = cachedMeData?.data?.current_role?.provider?.video;
  const description = cachedMeData?.data?.current_role?.provider?.description;

  useEffect(() => {
    const hasSeenAboutModal = localStorage.getItem("hasSeenAboutModal");
    if (!hasSeenAboutModal && (video || description)) {
      onAboutOpen();
    }
  }, [video, description]);
  return (
    <Modal
      size="2xl"
      isOpen={isAboutOpen}
      placement="center"
      className="mx-4"
      onOpenChange={onAboutClose}
    >
      <ModalContent className="rounded-small px-2 pb-5 pt-3 sm:px-4 md:px-6 md:pb-6 md:pt-4 lg:px-8 lg:pb-8">
        {() => (
          <>
            <ModalHeader className="flex flex-col gap-1" />
            <ModalBody className="gap-4 flex flex-col items-center">
              <p className="text-lg font-semibold">به باران خوش آمدید.</p>

              {!cachedMeData && (
                <>
                  <Skeleton className="rounded-lg w-full aspect-video h-full" />
                  <Skeleton className="rounded-lg w-full aspect-[16/4] h-full" />
                </>
              )}

              {cachedMeData && (
                <>
                  {video && (
                    <ReactPlayer
                      width={"100%"}
                      height={"100%"}
                      style={{
                        borderRadius: "0.5rem",
                        overflow: "hidden",
                        aspectRatio: 16 / 9,
                      }}
                      fallback={
                        <div className="w-full h-full rounded-lg bg-background-200 flex items-center justify-center">
                          <p className="text-sm text-foreground-400">
                            ویدیوی معرفی سازمان در حال بارگذاری است...
                          </p>
                        </div>
                      }
                      url={video}
                      controls
                      loop
                    />
                  )}
                  {description && (
                    <div className="max-h-36 overflow-y-auto w-full rounded-small border-2 border-dashed border-foreground-300 p-4">
                      <h2 className="text-lg font-medium mb-2">
                        {cachedMeData?.data?.current_role?.provider?.name}
                      </h2>
                      <p>{description}</p>
                    </div>
                  )}
                </>
              )}

              <FormCheckbox
                wrapperClassName="w-full"
                control={control}
                name="isSeen"
                label="دیگر نمایش نده"
              />
            </ModalBody>
            <ModalFooter>
              <Button
                color="primary"
                radius="full"
                size="lg"
                className="text-base px-10 font-medium"
                type="button"
                onPress={() => {
                  const values = getValues();
                  if (values.isSeen) {
                    localStorage.setItem("hasSeenAboutModal", "true");
                  }
                  onAboutClose();
                }}
              >
                متوجه شدم
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};

export default AboutModal;
