import PropTypes from 'prop-types';
import useSidebarStore from '../../stores/useSidebarStore';
import { motion } from 'framer-motion';

const Sidebar = ({ children }) => {
  const { isSidebarOpen } = useSidebarStore();
  return (
    <motion.div
      initial={{ width: 240 }}
      animate={{ width: isSidebarOpen ? 240 : 100 }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
      style={{ willChange: 'width' }}
      className={
        'relative z-50 flex flex-col bg-background pt-4 lg:border-e-1 lg:border-foreground-100/70'
      }>
      {children}
    </motion.div>
  );
};

Sidebar.propTypes = {
  children: PropTypes.node,
};

export default Sidebar;
