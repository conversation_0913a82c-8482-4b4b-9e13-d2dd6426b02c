import { cn } from "@heroui/react";
import PropTypes from "prop-types";
import { forwardRef } from "react";
import {
  AlignmentButton,
  BoldButton,
  HeadingButton,
  ImageButton,
  ItalicButton,
  LinkButton,
  OrderedListButton,
  RedoButton,
  StrikeButton,
  UnOrderedListButton,
  UnderlineButton,
  UndoButton,
} from "./EditorControllers";

const MenuBar = forwardRef(
  ({ editor, className, enabledButtons = {} }, ref) => {
    const defaultButtons = {
      bold: true,
      italic: true,
      underline: true,
      strike: true,
      heading: true,
      alignment: true,
      unorderedList: true,
      orderedList: true,
      image: true,
      link: true,
      undo: true,
      redo: true,
    };

    const buttons = { ...defaultButtons, ...enabledButtons };
    return (
      <ul
        ref={ref}
        className={cn(
          "flex w-full flex-wrap items-center divide-x-2 rtl:divide-x-reverse",
          className,
        )}
      >
        {/* Text Formatting Controls */}
        {(buttons.bold ||
          buttons.italic ||
          buttons.underline ||
          buttons.strike) && (
          <li className="space-x-2 space-x-reverse px-2 first:ps-0 last:pe-0">
            {buttons.bold && <BoldButton editor={editor} />}
            {buttons.italic && <ItalicButton editor={editor} />}
            {buttons.underline && <UnderlineButton editor={editor} />}
            {buttons.strike && <StrikeButton editor={editor} />}
          </li>
        )}

        {/* heading dropdown */}
        {buttons.heading && (
          <li className="space-x-2 space-x-reverse px-2 first:ps-0 last:pe-0">
            <HeadingButton editor={editor} />
          </li>
        )}

        {/* alignment dropdown  */}
        {buttons.alignment && (
          <li className="space-x-2 space-x-reverse px-2 first:ps-0 last:pe-0">
            <AlignmentButton editor={editor} />
          </li>
        )}

        {/* list buttons  */}
        {(buttons.unorderedList || buttons.orderedList) && (
          <li className="space-x-2 text-danger space-x-reverse px-2 first:ps-0 last:pe-0">
            {buttons.unorderedList && <UnOrderedListButton editor={editor} />}
            {buttons.orderedList && <OrderedListButton editor={editor} />}
          </li>
        )}

        {/* image and link buttons  */}
        {(buttons.image || buttons.link) && (
          <li className="space-x-2 space-x-reverse px-2 first:ps-0 last:pe-0">
            {buttons.image && <ImageButton editor={editor} />}
            {buttons.link && <LinkButton editor={editor} />}
          </li>
        )}

        {/* undo and redo button  */}
        {(buttons.undo || buttons.redo) && (
          <li className="space-x-2 space-x-reverse px-2 first:ps-0 last:pe-0">
            {buttons.undo && <UndoButton editor={editor} />}
            {buttons.redo && <RedoButton editor={editor} />}
          </li>
        )}
      </ul>
    );
  },
);

MenuBar.displayName = "MenuBar";

export default MenuBar;

MenuBar.propTypes = {
  editor: PropTypes.object.isRequired,
  className: PropTypes.string,
  enabledButtons: PropTypes.object,
};
