services:
  app:
    container_name: 'account'
    build:
      context: .
      dockerfile: Dockerfile
    image: ghcr.io/barangroup-ir/baran_account:latest
    labels:
      # Enable Traefik for this service
      - 'traefik.enable=true'

      # Define the network Traefik is using
      - 'traefik.docker.network=traefik'

      # Route for account.barangroup.ir -> port 3000
      - 'traefik.http.routers.account1-dashboard.rule=Host(`account1.barangroup.ir`)'
      - 'traefik.http.routers.account1-dashboard.service=account1-service'
      - 'traefik.http.routers.account1-dashboard.entrypoints=websecure'
      - 'traefik.http.routers.account1-dashboard.tls=true'
      - 'traefik.http.services.account1-service.loadbalancer.server.port=3000'
    networks:
      - traefik
    environment:
      - TZ=Asia/Tehran
      - NODE_ENV=production
    volumes:
      - account:/app
    expose:
      - '3000'
networks:
  traefik:
    external: true
volumes:
  account:
