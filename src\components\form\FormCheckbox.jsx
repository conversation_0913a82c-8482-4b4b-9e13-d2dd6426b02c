import { Checkbox, cn } from "@heroui/react";
import PropTypes from "prop-types";
import { useController } from "react-hook-form";

/**
 * @param {FormCheckboxProps} props
 */
const FormCheckbox = (props) => {
  const { name, control, className, children, wrapperClassName, ...other } =
    props;

  const {
    field: { onChange, value, ...field },
    fieldState,
  } = useController({ name, control });

  const defaultProps = {
    className: cn("w-full z-0", className),
    ...other,
  };

  return (
    <div className={cn("flex flex-col gap-2", wrapperClassName)}>
      <Checkbox
        {...field}
        isInvalid={!!fieldState.error}
        onValueChange={onChange}
        isSelected={value}
        {...defaultProps}
      >
        {children}
      </Checkbox>
      {fieldState.error && (
        <p className="text-sm text-red-500">{fieldState.error.message}</p>
      )}
    </div>
  );
};

FormCheckbox.propTypes = {
  name: PropTypes.string.isRequired,
  control: PropTypes.object.isRequired,
  className: PropTypes.string,
  wrapperClassName: PropTypes.string,
  children: PropTypes.node,
};

export default FormCheckbox;
