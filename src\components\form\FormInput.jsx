import { Input, NumberInput, cn } from "@heroui/react";
import PropTypes from "prop-types";
import { useController } from "react-hook-form";
import convertInvalidCharacter from "./../../utils/convertInvalidCharacter";

/**
 * @param {FormInputProps} props
 */
const FormInput = ({ control, name, type, rules, inputProps = {} }) => {
  const {
    field: { onChange, value, ...field },
    fieldState,
  } = useController({
    name,
    control,
    rules: rules,
  });

  const handleInvalidCharacter = (val) => {
    const newValue = type === "number" ? val : convertInvalidCharacter(val);
    if (newValue !== value) {
      onChange(newValue);
    }
  };

  const Component = type === "number" ? NumberInput : Input;

  return (
    <Component
      {...field}
      value={value}
      hideStepper
      type={type !== "number" && type}
      formatOptions={{
        useGrouping: type === "number",
      }}
      onValueChange={handleInvalid<PERSON>haracter}
      isInvalid={!!fieldState.error}
      errorMessage={fieldState.error?.message}
      classNames={{
        inputWrapper: cn(
          "hover:bg-background-50 dark:hover:bg-background-50",
          { "bg-background-100": !fieldState.error },
          inputProps?.classNames?.inputWrapper,
        ),
        ...(({ inputWrapper, ...rest }) => rest)(inputProps?.classNames || {}),
      }}
      {...inputProps}
    />
  );
};

FormInput.propTypes = {
  control: PropTypes.object.isRequired,
  name: PropTypes.string.isRequired,
  rules: PropTypes.object,
  type: PropTypes.oneOf(["text", "number"]),
  inputProps: PropTypes.shape({
    classNames: PropTypes.shape({
      inputWrapper: PropTypes.string,
    }),
  }),
};

export default FormInput;
