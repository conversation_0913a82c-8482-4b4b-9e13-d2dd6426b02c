import { Skeleton, addToast } from "@heroui/react";
import { useEffect, useMemo } from "react";
import ReactPlayer from "react-player";
import PageWrapper from "../../components/layout/PageWrapper";
import api from "./../../api/index";

const AboutPage = () => {
  const { data: _data, isLoading, error } = api.About.detail.useQuery();
  const data = useMemo(() => _data?.data, [_data]);
  useEffect(() => {
    if (_data?.status === false || error) {
      addToast({
        title: _data?.message ? _data?.message : "مشکلی پیش آمده است",
        color: "danger",
      });
    }
  }, [error, _data]);

  return (
    <PageWrapper>
      <div className="flex flex-col items-center justify-center min-h-64 gap-4 rounded-small bg-background px-4 py-7  transition-all duration-300 md:py-8 lg:py-10 ">
        <div className="max-w-3xl w-full flex flex-col gap-4">
          {isLoading && (
            <>
              <Skeleton className=" rounded-lg w-full aspect-video h-full" />
              <Skeleton className=" rounded-lg w-full aspect-[16/4] h-full" />
            </>
          )}
          {/* Introduction video */}
          {!isLoading && data && (
            <>
              {data.video && (
                <ReactPlayer
                  width={"100%"}
                  height={"100%"}
                  fallback={
                    <div className="w-full h-full rounded-lg bg-background-200 flex items-center justify-center">
                      <p className="text-sm text-foreground-400">
                        ویدیوی معرفی سازمان در حال بارگذاری است...
                      </p>
                    </div>
                  }
                  style={{
                    borderRadius: "0.5rem",
                    overflow: "hidden",
                    aspectRatio: 16 / 9,
                  }}
                  url={data.video}
                  controls
                  loop
                />
              )}

              {data.description && (
                <div className="w-full rounded-small border-2 border-dashed border-foreground-300 p-4">
                  <h2 className="text-lg font-medium mb-2">{data?.name}</h2>
                  <p>{data.description}</p>
                </div>
              )}

              {!data.description && !data.video && (
                <h2 className="text-center font-medium md:text-lg ">
                  توضیحاتی راجب این سازمان وجود ندارد
                </h2>
              )}
            </>
          )}
        </div>
      </div>
    </PageWrapper>
  );
};

export default AboutPage;
