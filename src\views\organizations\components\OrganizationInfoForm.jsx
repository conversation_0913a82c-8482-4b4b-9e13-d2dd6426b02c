import { Edit } from "iconsax-reactjs";
import PropTypes from "prop-types";
import FormDatePicker from "../../../components/form/FormDatePicker";
import FormInput from "../../../components/form/FormInput";
import FormRichText from "../../../components/form/FormRichText/FormRichText";
import FormSwitch from "../../../components/form/FormSwitch";
import FormUpload from "../../../components/form/FormUpload";
import FormUploadAvatar from "../../../components/form/FormUploadAvatar";
import Icon from "../../../components/icon/Icon";

const OrganizationInfoForm = ({ control }) => {
  return (
    <div className="flex flex-col gap-6 rounded-small bg-background px-6 pb-7 pt-4 text-sm sm:text-base md:px-7 md:pb-8 lg:px-12 lg:pb-8 lg:pt-10">
      <FormUploadAvatar
        name="image"
        control={control}
        classNames={{
          wrapper: "max-h-28 self-center md:self-auto max-w-28",
        }}
      />

      <p className="font-medium mt-2">اطلاعات سازمان</p>

      <div className="flex items-start flex-wrap md:flex-nowrap gap-4">
        <FormInput
          control={control}
          name="name"
          type="text"
          inputProps={{
            classNames: {
              base: "max-w-sm",
              inputWrapper:
                "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
              input: "text-sm",
            },
            size: "lg",
            radius: "full",
            placeholder: "نام سازمان",
            startContent: <Edit className="size-6 text-primary" />,
          }}
        />
        <FormDatePicker
          control={control}
          name="contract_date"
          isRange={true}
          size="lg"
          radius="full"
          startContent={
            <Icon className={"text-primary size-7"} name={"accept-note"} />
          }
          classNames={{
            base: "max-w-sm",
            inputWrapper:
              "shadow-sm hover:!bg-background-100 border hover:border-foreground-200 transition-colors border-foreground-100",
            input: "text-sm ",
          }}
        />
      </div>

      <p className="font-medium mt-2">ویدیوی مرتبط با سازمان</p>

      <FormUpload control={control} name="video" fileTypeRestriction="video" />

      <p className="font-medium mt-2">توضیحات مرتبط با سازمان</p>
      <FormRichText
        control={control}
        name="description"
        enabledButtons={{
          image: false,
          link: false,
        }}
      />

      <FormSwitch
        control={control}
        className="ltr"
        name="active"
        label="وضعیت سازمان"
      />
    </div>
  );
};

OrganizationInfoForm.propTypes = {
  control: PropTypes.object.isRequired,
};

export default OrganizationInfoForm;
