import { useController } from 'react-hook-form';
import convertInvalidCharacter from '../../utils/convertInvalidCharacter';
import { cn, InputOtp } from '@heroui/react';
import PropTypes from 'prop-types';

const FormOtp = ({ control, name, className, classNames, ...other }) => {
  const {
    field: { onChange, value, ...field },
    fieldState,
  } = useController({
    name,
    control,
  });

  const handleInvalidCharacter = (val) => {
    const newValue = convertInvalidCharacter(val);
    if (newValue !== value) {
      onChange(newValue);
    }
  };

  const defaultProps = {
    description: 'کد تایید را وارد کنید',
    className: cn('w-full z-0 grid place-items-center', className),
    classNames: {
      errorMessage: cn('rtl', classNames?.errorMessage),
      ...classNames,
    },
    ...other,
  };

  return (
    <div className='ltr'>
      <InputOtp
        {...field}
        onValueChange={handleInvalidCharacter}
        isInvalid={!!fieldState.error}
        errorMessage={fieldState.error?.message}
        {...defaultProps}
      />
    </div>
  );
};

FormOtp.propTypes = {
  name: PropTypes.string.isRequired,
  control: PropTypes.object.isRequired,
  radius: PropTypes.oneOf(['none', 'sm', 'md', 'lg', 'full']),
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  length: PropTypes.number,
  className: PropTypes.string,
  description: PropTypes.string,
  variant: PropTypes.oneOf(['faded', 'bordered', 'flat', 'underline']),
  color: PropTypes.string,
  classNames: PropTypes.shape({
    base: PropTypes.string,
    input: PropTypes.string,
    errorMessage: PropTypes.string,
    description: PropTypes.string,
    inputWrapper: PropTypes.string,
    caret: PropTypes.string,
    segment: PropTypes.string,
    segmentWrapper: PropTypes.string,
    passwordChar: PropTypes.string,
    helperWrapper: PropTypes.string,
  }),
};

export default FormOtp;
