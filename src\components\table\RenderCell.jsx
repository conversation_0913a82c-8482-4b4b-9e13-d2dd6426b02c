import { Chip, User, cn } from "@heroui/react";
import dayjs from "dayjs";
import { CloseSquare } from "iconsax-reactjs";
import { TickSquare } from "iconsax-reactjs";
import jalaliday from "jalaliday";
import PropTypes from "prop-types";

dayjs.extend(jalaliday);
const RenderCell = ({ item, column, index, pagination }) => {
  const rowNumber =
    (pagination?.current_page - 1) * pagination?.per_page + index + 1;

  const field =
    typeof column.field === "function"
      ? column.field(item)
      : item[column.field];

  switch (column?.type) {
    case "rowNumber":
      return rowNumber;
    case "user":
      return (
        <User
          className="flex text-foreground w-full justify-start pb-2"
          classNames={{
            name: "font-semibold text-start",
            description: "text-sm",
          }}
          avatarProps={{
            radius: "full",
            src: field?.avatar,
            className: "shrink-0",
          }}
          name={field?.name}
          description={field?.description}
        />
      );
    case "product":
      return (
        <User
          avatarProps={{
            radius: "lg",
            src: field?.image[0],

            className: "shrink-0",
          }}
          name={field?.title}
          description={field?.description}
        />
      );
    case "date":
      return (
        <span dir="ltr">
          {dayjs(field)
            .calendar("jalali")
            .locale("fa")
            .format("YYYY/MM/DD HH:mm")}
        </span>
      );
    case "chip":
      return (
        <Chip color={column?.typeVariable[field?.key]} size="sm" variant="flat">
          {field?.value}
        </Chip>
      );
    case "status":
      return (
        <p
          className={cn("text-sm font-medium md:text-base", {
            "text-secondary": field,
            "text-danger": !field,
          })}
        >
          {field ? "فعال" : "غیرفعال"}
        </p>
      );
    case "active":
      return (
        <div className="flex w-full items-center justify-center">
          {field ? (
            <TickSquare className="size-6 text-success" variant="Bulk" />
          ) : (
            <CloseSquare className="size-6 text-danger" variant="Bulk" />
          )}
        </div>
      );
    case "custom":
      return field;
    default: {
      return (
        <span className="font-medium text-foreground lg:text-base">
          {field
            ? typeof field === "object"
              ? JSON.stringify(field)
              : field
            : "_"}
        </span>
      );
    }
  }
};

RenderCell.propTypes = {
  item: PropTypes.object.isRequired,
  column: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  pagination: PropTypes.object.isRequired,
};

export default RenderCell;
