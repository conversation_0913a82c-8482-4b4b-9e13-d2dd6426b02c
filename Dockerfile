# Base image for building the apps
FROM node:22.8.0 as base

# Set working directory
WORKDIR /app

# Copy package files and install dependencies early to take advantage of <PERSON><PERSON>'s cache
COPY ./package.json ./

RUN yarn

# Copy the rest of the files
COPY . .

###############################################
# Stage 3: Build the user app
###############################################
FROM base as build

RUN yarn build

###############################################
# Stage 4: Serve the built apps in production
###############################################
FROM node:22.8.0-alpine as production

# Install the 'serve' package to serve static files
RUN npm install serve -g

# Working directory for the final app
WORKDIR /src

# Copy the built admin, teacher, and user apps from previous stages
COPY --from=build /app/dist /src/dist

# Expose the ports for the different applications
EXPOSE 3000

# Serve the admin, teacher, and user apps on different ports
CMD ["sh", "-c", "serve -s /src/dist -l 3000"]
