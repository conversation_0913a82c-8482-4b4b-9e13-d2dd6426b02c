import { Chip, Input, cn } from "@heroui/react";
import { useDebounce } from "@uidotdev/usehooks";
import { SearchNormal1 } from "iconsax-reactjs";
import { useQueryState } from "nuqs";
import PropTypes from "prop-types";
import { useEffect, useState } from "react";

/**
 * @param {SearchFilterProps} props
 */
const SearchFilter = ({ name, value, setValue, ...other }) => {
  const [filter, setFilter] = useQueryState(name, { defaultValue: "" });
  const [inputValue, setInputValue] = useState(value ?? filter);
  const debouncedValue = useDebounce(inputValue, 2000);

  useEffect(() => {
    if (setValue) {
      setValue(debouncedValue);
    } else {
      setFilter(debouncedValue || null);
    }
  }, [debouncedValue]);

  useEffect(() => {
    const currentValue = value ?? filter;
    setInputValue(currentValue);
  }, [value, filter]);

  return (
    <Input
      value={inputValue}
      onValueChange={setInputValue}
      startContent={
        <SearchNormal1 className={"size-5 shrink-0 text-foreground-300"} />
      }
      {...other}
    />
  );
};

SearchFilter.propTypes = {
  name: PropTypes.string.isRequired,
  value: PropTypes.string,
  setValue: PropTypes.func,
  historyProps: PropTypes.shape({
    className: PropTypes.string,
    chipClassName: PropTypes.string,
    chipSize: PropTypes.oneOf(["sm", "md", "lg"]),
    chipRadius: PropTypes.oneOf(["none", "sm", "md", "lg", "full"]),
    chipColor: PropTypes.oneOf([
      "primary",
      "secondary",
      "success",
      "warning",
      "danger",
    ]),
    chipVariant: PropTypes.oneOf([
      "solid",
      "bordered",
      "light",
      "flat",
      "dot",
      "faded",
      "shadow",
    ]),
  }),
};

const History = ({ name, label, value, setValue, historyProps }) => {
  const [filter, setFilter] = useQueryState(name);
  const currentValue = value ?? filter;

  if (!currentValue?.length) {
    return null;
  }

  return (
    <div
      className={cn(
        "flex w-max max-w-lg flex-wrap items-center gap-2 rounded-md border border-dashed border-foreground-100 bg-background px-1.5 py-2 font-medium",
        historyProps?.className,
      )}
    >
      {label && <p className="text-sm">{label}:</p>}
      <Chip
        radius={historyProps?.chipRadius ?? "sm"}
        size={historyProps?.chipSize ?? "sm"}
        color={historyProps?.chipColor}
        variant={historyProps?.chipVariant}
        className={cn("p-1.5 text-sm", historyProps?.chipClassName)}
        onClose={() => {
          if (setValue) {
            setValue("");
          } else {
            setFilter(null);
          }
        }}
      >
        {currentValue}
      </Chip>
    </div>
  );
};

History.propTypes = {
  label: PropTypes.string,
  name: PropTypes.string.isRequired,
  value: PropTypes.string,
  setValue: PropTypes.func,
  historyProps: PropTypes.shape({
    className: PropTypes.string,
    chipClassName: PropTypes.string,
    chipSize: PropTypes.oneOf(["sm", "md", "lg"]),
    chipRadius: PropTypes.oneOf(["none", "sm", "md", "lg", "full"]),
    chipColor: PropTypes.oneOf([
      "primary",
      "secondary",
      "success",
      "warning",
      "danger",
    ]),
    chipVariant: PropTypes.oneOf([
      "solid",
      "bordered",
      "light",
      "flat",
      "dot",
      "faded",
      "shadow",
    ]),
  }),
};

SearchFilter.History = History;
History.displayName = "search-filter-history";
SearchFilter.showInDrawer = true;

export default SearchFilter;
