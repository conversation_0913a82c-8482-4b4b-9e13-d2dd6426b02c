import useSidebarStore from '../../stores/useSidebarStore';
import PropTypes from 'prop-types';
import SimpleBar from 'simplebar-react';

const SidebarMenu = ({ children }) => {
  const { isSidebarOpen } = useSidebarStore();

  return (
    <div className='relative flex-1'>
      <SimpleBar style={{ maxHeight: 'calc(100vh - 90px)' }}>
        <ul className={'relative flex flex-col'}>{children}</ul>
      </SimpleBar>
    </div>
  );
};

SidebarMenu.propTypes = {
  children: PropTypes.node.isRequired,
};

export default SidebarMenu;
