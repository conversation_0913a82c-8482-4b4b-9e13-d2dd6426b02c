import { router } from "react-query-kit";
import createFetcher from "../utils/createFetcher";

const Organizations = router("organizations", {
  list: router.query({
    fetcher: createFetcher("/v3/organizations"),
  }),
  detail: router.query({
    fetcher: createFetcher("/v3/organizations/:id/report"),
  }),
  add: router.mutation({
    mutationFn: createFetcher("/v3/organizations", "POST"),
  }),
});

export default Organizations;
