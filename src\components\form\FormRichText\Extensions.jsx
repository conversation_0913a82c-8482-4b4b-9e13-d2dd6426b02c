import BulletList from "@tiptap/extension-bullet-list";
import Heading from "@tiptap/extension-heading";
import Image from "@tiptap/extension-image";
import Link from "@tiptap/extension-link";
import OrderedList from "@tiptap/extension-ordered-list";
import TextAlign from "@tiptap/extension-text-align";
import Underline from "@tiptap/extension-underline";
import StarterKit from "@tiptap/starter-kit";

const extensions = [
  StarterKit.configure({
    heading: false,
    bulletList: false,
    orderedList: false,
  }),
  Heading.configure({
    levels: [1, 2, 3, 4, 5, 6],
  }),

  BulletList.configure({
    HTMLAttributes: {
      class: 'list-disc ms-4',
    },
  }),
  OrderedList.configure({
    HTMLAttributes: {
      class: 'list-decimal ms-4',
    },
  }),
  TextAlign.configure({
    types: ['heading', 'paragraph'],
    alignments: ['left', 'center', 'right', 'justify'],
  }),
  Underline,
  Image.configure({
    HTMLAttributes: {
      class: 'max-w-full rounded-medium',
    },
  }),
  Link.configure({
    openOnClick: false,
    HTMLAttributes: {
      class: 'text-primary-500 underline',
    },
  }),
];

export default extensions;
