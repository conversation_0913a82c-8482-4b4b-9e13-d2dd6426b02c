import {
  <PERSON><PERSON>,
  Dropdown,
  Dropdown<PERSON><PERSON>,
  DropdownMenu,
  DropdownTrigger,
  Input,
  Modal,
  ModalBody,
  ModalContent,
  <PERSON>dal<PERSON>ooter,
  ModalHeader,
  cn,
  useDisclosure,
} from "@heroui/react";
import {
  ArrowForward,
  Back,
  Image,
  Link,
  TextBold,
  TextItalic,
  TextUnderline,
  TextalignCenter,
  TextalignJustifycenter,
  TextalignLeft,
  TextalignRight,
} from "iconsax-reactjs";
import PropTypes from "prop-types";
import { useState } from "react";
import Icon from "../../icon/Icon";

const buttonPropTypes = {
  size: PropTypes.oneOf(["sm", "md", "lg"]),
  radius: PropTypes.oneOf(["none", "sm", "md", "lg", "full"]),
  activeColor: PropTypes.oneOf([
    "primary",
    "secondary",
    "success",
    "warning",
    "danger",
  ]),
  inactiveColor: PropTypes.oneOf([
    "primary",
    "secondary",
    "success",
    "warning",
    "danger",
  ]),
  activeVariant: PropTypes.oneOf([
    "solid",
    "light",
    "flat",
    "faded",
    "shadow",
    "ghost",
    "bordered",
  ]),
  inactiveVariant: PropTypes.oneOf([
    "solid",
    "light",
    "flat",
    "faded",
    "shadow",
    "ghost",
    "bordered",
  ]),
  iconClassName: PropTypes.string,
  editor: PropTypes.object.isRequired,
};

const dropdownButtonPropTypes = {
  editor: PropTypes.object.isRequired,
  triggerActiveColor: PropTypes.oneOf([
    "primary",
    "secondary",
    "success",
    "warning",
    "danger",
  ]),
  triggerInactiveColor: PropTypes.oneOf([
    "primary",
    "secondary",
    "success",
    "warning",
    "danger",
  ]),
  triggerActiveVariant: PropTypes.oneOf([
    "solid",
    "light",
    "flat",
    "faded",
    "shadow",
    "ghost",
    "bordered",
  ]),
  triggerInactiveVariant: PropTypes.oneOf([
    "solid",
    "light",
    "flat",
    "faded",
    "shadow",
    "ghost",
    "bordered",
  ]),
  triggerIconClassName: PropTypes.string,
  className: PropTypes.string,
  menuClassName: PropTypes.string,
  size: PropTypes.oneOf(["sm", "md", "lg"]),
  variant: PropTypes.oneOf([
    "solid",
    "light",
    "flat",
    "faded",
    "shadow",
    "ghost",
    "bordered",
  ]),
  color: PropTypes.oneOf([
    "primary",
    "secondary",
    "success",
    "warning",
    "danger",
  ]),
  triggerSize: PropTypes.oneOf(["sm", "md", "lg"]),
  radius: PropTypes.oneOf(["none", "sm", "md", "lg", "full"]),
  triggerRadius: PropTypes.oneOf(["none", "sm", "md", "lg", "full"]),
};

export const BoldButton = ({
  editor,
  size = "sm",
  radius,
  activeColor = "primary",
  inactiveColor = "default",
  activeVariant = "flat",
  inactiveVariant = "light",
  iconClassName,
}) => {
  return (
    <Button
      size={size}
      radius={radius}
      isIconOnly
      color={editor.isActive("bold") ? activeColor : inactiveColor}
      variant={editor.isActive("bold") ? activeVariant : inactiveVariant}
      onPress={() => editor.chain().focus().toggleBold().run()}
    >
      <TextBold className={cn("size-5", iconClassName)} />
    </Button>
  );
};

BoldButton.propTypes = buttonPropTypes;

export const ItalicButton = ({
  editor,
  size = "sm",
  radius,
  activeColor = "primary",
  inactiveColor = "default",
  activeVariant = "flat",
  inactiveVariant = "light",
  iconClassName,
}) => {
  return (
    <Button
      size={size}
      radius={radius}
      isIconOnly
      color={editor.isActive("italic") ? activeColor : inactiveColor}
      variant={editor.isActive("italic") ? activeVariant : inactiveVariant}
      onPress={() => editor.chain().focus().toggleItalic().run()}
    >
      <TextItalic className={cn("size-5", iconClassName)} />
    </Button>
  );
};

ItalicButton.propTypes = buttonPropTypes;

export const UnderlineButton = ({
  editor,
  size = "sm",
  radius,
  activeColor = "primary",
  inactiveColor = "default",
  activeVariant = "flat",
  inactiveVariant = "light",
  iconClassName,
}) => {
  return (
    <Button
      size={size}
      radius={radius}
      isIconOnly
      color={editor.isActive("underline") ? activeColor : inactiveColor}
      variant={editor.isActive("underline") ? activeVariant : inactiveVariant}
      onPress={() => editor.chain().focus().toggleUnderline().run()}
    >
      <TextUnderline className={cn("size-5", iconClassName)} />
    </Button>
  );
};

UnderlineButton.propTypes = buttonPropTypes;

export const StrikeButton = ({
  editor,
  size = "sm",
  radius,
  activeColor = "primary",
  inactiveColor = "default",
  activeVariant = "flat",
  inactiveVariant = "light",
  iconClassName,
}) => {
  return (
    <Button
      size={size}
      radius={radius}
      isIconOnly
      color={editor.isActive("strike") ? activeColor : inactiveColor}
      variant={editor.isActive("strike") ? activeVariant : inactiveVariant}
      onPress={() => editor.chain().focus().toggleStrike().run()}
    >
      <Icon
        name="strikeThrough"
        className={cn("size-5 text-inherit", iconClassName)}
      />
    </Button>
  );
};

StrikeButton.propTypes = buttonPropTypes;

export const HeadingButton = ({
  editor,
  className,
  size,
  variant,
  color,
  menuClassName,
  radius = "sm",
  triggerSize = "sm",
  triggerRadius = "sm",
  triggerActiveColor = "primary",
  triggerInactiveColor = "default",
  triggerActiveVariant = "flat",
  triggerInactiveVariant = "light",
  triggerIconClassName,
}) => {
  const headingLevels = [
    {
      level: 1,
      icon: (
        <Icon
          name="heading-one"
          className={cn("size-5 text-inherit", triggerIconClassName)}
        />
      ),
    },
    {
      level: 2,
      icon: (
        <Icon
          name="heading-two"
          className={cn("size-5 text-inherit", triggerIconClassName)}
        />
      ),
    },
    {
      level: 3,
      icon: (
        <Icon
          name="heading-three"
          className={cn("size-5 text-inherit", triggerIconClassName)}
        />
      ),
    },
    {
      level: 4,
      icon: (
        <Icon
          name="heading-four"
          className={cn("size-5 text-inherit", triggerIconClassName)}
        />
      ),
    },
    {
      level: 5,
      icon: (
        <Icon
          name="heading-five"
          className={cn("size-5 text-inherit", triggerIconClassName)}
        />
      ),
    },
    {
      level: 6,
      icon: (
        <Icon
          name="heading-six"
          className={cn("size-5 text-inherit", triggerIconClassName)}
        />
      ),
    },
  ];

  const handleHeadingChange = (level) => {
    if (editor.isActive("heading", { level: level })) {
      editor.chain().focus().toggleHeading({ level: level }).run();
    } else {
      editor.chain().focus().toggleHeading({ level: level }).run();
    }
  };

  return (
    <Dropdown
      radius={radius}
      size={size}
      showArrow={true}
      className={cn("min-w-3 p-0", className)}
    >
      <DropdownTrigger>
        <Button
          size={triggerSize}
          radius={triggerRadius}
          color={
            editor.isActive("heading")
              ? triggerActiveColor
              : triggerInactiveColor
          }
          isIconOnly
          variant={
            editor.isActive("heading")
              ? triggerActiveVariant
              : triggerInactiveVariant
          }
        >
          {editor.isActive("heading") ? (
            headingLevels.find(
              (level) => level.level === editor.getAttributes("heading").level,
            )?.icon
          ) : (
            <Icon
              name="heading"
              className={cn("size-5 text-inherit", triggerIconClassName)}
            />
          )}
        </Button>
      </DropdownTrigger>

      <DropdownMenu
        disallowEmptySelection
        aria-label="Heading"
        selectedKeys={
          editor.isActive("heading") ? [editor.isActive("heading")] : []
        }
        selectionMode="single"
        variant={variant}
        color={color}
        className={menuClassName}
        classNames={{
          base: "w-max p-0",
          list: "flex-row gap-2 px-2",
        }}
        onSelectionChange={handleHeadingChange}
      >
        {headingLevels.map((item) => {
          return (
            <DropdownItem
              classNames={{
                selectedIcon: "hidden",
                base: "py-1 data-[hover=true]:bg-transparent data-[selectable=true]:focus:bg-transparent data-[focus=true]:bg-transparent px-0 ",
              }}
              key={item.level}
            >
              <Button
                size={triggerSize}
                radius={triggerRadius}
                isIconOnly
                color={
                  editor.isActive("heading", { level: item.level })
                    ? triggerActiveColor
                    : triggerInactiveColor
                }
                variant={
                  editor.isActive("heading", { level: item.level })
                    ? triggerActiveVariant
                    : triggerInactiveVariant
                }
                onPress={() => handleHeadingChange(item.level)}
              >
                {item.icon}
              </Button>
            </DropdownItem>
          );
        })}
      </DropdownMenu>
    </Dropdown>
  );
};

HeadingButton.propTypes = dropdownButtonPropTypes;

export const AlignmentButton = ({
  editor,
  className,
  size,
  variant,
  color,
  menuClassName,
  radius = "sm",
  triggerSize = "sm",
  triggerRadius = "sm",
  triggerActiveColor = "primary",
  triggerInactiveColor = "default",
  triggerActiveVariant = "flat",
  triggerInactiveVariant = "light",
  triggerIconClassName,
}) => {
  const alignmentIcons = [
    {
      key: "right",
      icon: <TextalignRight className={cn("size-5", triggerIconClassName)} />,
    },
    {
      key: "left",
      icon: <TextalignLeft className={cn("size-5", triggerIconClassName)} />,
    },
    {
      key: "center",
      icon: <TextalignCenter className={cn("size-5", triggerIconClassName)} />,
    },
    {
      key: "justify",
      icon: (
        <TextalignJustifycenter
          className={cn("size-5", triggerIconClassName)}
        />
      ),
    },
  ];

  const handleAlignmentChange = (alignment) => {
    if (editor.isActive({ textAlign: alignment })) {
      editor.chain().focus().unsetTextAlign().run();
    } else {
      editor.chain().focus().setTextAlign(alignment).run();
    }
  };
  return (
    <Dropdown
      radius={radius}
      size={size}
      showArrow={true}
      className={cn("min-w-3 p-0", className)}
    >
      <DropdownTrigger>
        <Button
          size={triggerSize}
          radius={triggerRadius}
          color={
            alignmentIcons.reduce(
              (acc, item) => acc || editor.isActive({ textAlign: item.key }),
              false,
            )
              ? triggerActiveColor
              : triggerInactiveColor
          }
          isIconOnly
          variant={
            alignmentIcons.reduce(
              (acc, item) => acc || editor.isActive({ textAlign: item.key }),
              false,
            )
              ? triggerActiveVariant
              : triggerInactiveVariant
          }
        >
          {alignmentIcons.find((icon) =>
            editor.isActive({ textAlign: icon.key }),
          )?.icon || (
            <TextalignRight className={cn("size-5", triggerIconClassName)} />
          )}
        </Button>
      </DropdownTrigger>

      <DropdownMenu
        disallowEmptySelection
        aria-label="Alignment"
        selectionMode="single"
        variant={variant}
        color={color}
        className={menuClassName}
        classNames={{
          base: "w-max  p-0",
          list: "flex-row  gap-2 px-2",
        }}
        onSelectionChange={handleAlignmentChange}
      >
        {alignmentIcons.map((item) => {
          return (
            <DropdownItem
              classNames={{
                selectedIcon: "hidden",
                base: "py-1 data-[hover=true]:bg-transparent data-[selectable=true]:focus:bg-transparent data-[focus=true]:bg-transparent px-0 ",
              }}
              key={item.key}
            >
              <Button
                size={triggerSize}
                radius={triggerRadius}
                isIconOnly
                color={
                  editor.isActive({ textAlign: item.key })
                    ? triggerActiveColor
                    : triggerInactiveColor
                }
                variant={
                  editor.isActive({ textAlign: item.key })
                    ? triggerActiveVariant
                    : triggerInactiveVariant
                }
                onPress={() => handleAlignmentChange(item.key)}
              >
                {item.icon}
              </Button>
            </DropdownItem>
          );
        })}
      </DropdownMenu>
    </Dropdown>
  );
};

AlignmentButton.propTypes = dropdownButtonPropTypes;

export const UnOrderedListButton = ({
  editor,
  size = "sm",
  radius,
  activeColor = "primary",
  inactiveColor = "default",
  activeVariant = "flat",
  inactiveVariant = "light",
  iconClassName,
}) => {
  return (
    <Button
      size={size}
      radius={radius}
      isIconOnly
      color={editor.isActive("bulletList") ? activeColor : inactiveColor}
      variant={editor.isActive("bulletList") ? activeVariant : inactiveVariant}
      onPress={() => editor.chain().focus().toggleBulletList().run()}
    >
      <Icon
        name="unordered-list"
        className={cn("size-5 text-inherit", iconClassName)}
      />
    </Button>
  );
};

UnOrderedListButton.propTypes = buttonPropTypes;

export const OrderedListButton = ({
  editor,
  size = "sm",
  radius,
  activeColor = "primary",
  inactiveColor = "default",
  activeVariant = "flat",
  inactiveVariant = "light",
  iconClassName,
}) => {
  return (
    <Button
      size={size}
      radius={radius}
      isIconOnly
      color={editor.isActive("orderedList") ? activeColor : inactiveColor}
      variant={editor.isActive("orderedList") ? activeVariant : inactiveVariant}
      onPress={() => editor.chain().focus().toggleOrderedList().run()}
    >
      <Icon
        name="ordered-list"
        className={cn("size-5 text-inherit", iconClassName)}
      />
    </Button>
  );
};

OrderedListButton.propTypes = buttonPropTypes;

export const ImageButton = ({
  editor,
  size = "sm",
  radius,
  activeColor = "primary",
  inactiveColor = "default",
  activeVariant = "flat",
  inactiveVariant = "light",
  iconClassName,
}) => {
  const [value, setValue] = useState("");

  const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();

  const handleSubmit = () => {
    if (value.trim()) {
      editor.chain().focus().setImage({ src: value }).run();
      onClose();
    }
  };

  return (
    <>
      <Button
        size={size}
        radius={radius}
        isIconOnly
        color={editor.isActive("image") ? activeColor : inactiveColor}
        variant={editor.isActive("image") ? activeVariant : inactiveVariant}
        onPress={() => {
          onOpen();
        }}
      >
        <Image className={cn("size-5", iconClassName)} />
      </Button>

      <Modal isOpen={isOpen} onOpenChange={onOpenChange} placement="center">
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h3 className="text-lg font-semibold">وارد کردن تصویر</h3>
            <p className="text-sm text-default-500">
              لطفا آدرس تصویر مورد نظر را وارد کنید
            </p>
          </ModalHeader>
          <ModalBody>
            <Input
              value={value}
              onValueChange={setValue}
              placeholder="https://example.com/image.jpg"
              variant="bordered"
              radius="sm"
              classNames={{
                label: "text-foreground-600 font-medium",
                input: "text-foreground-700",
              }}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleSubmit();
                }
              }}
              endContent={
                <Image className="pointer-events-none flex-shrink-0 text-foreground-400" />
              }
            />
          </ModalBody>
          <ModalFooter>
            <Button
              variant="light"
              onPress={() => {
                setValue(editor.getAttributes("image").src ?? "");
                onClose();
              }}
              radius="sm"
            >
              انصراف
            </Button>
            <Button
              color="primary"
              onPress={handleSubmit}
              radius="sm"
              isDisabled={!value.trim()}
            >
              تایید
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
};

ImageButton.propTypes = buttonPropTypes;

export const LinkButton = ({
  editor,
  size = "sm",
  radius,
  activeColor = "primary",
  inactiveColor = "default",
  activeVariant = "flat",
  inactiveVariant = "light",
  iconClassName,
}) => {
  const [value, setValue] = useState("");
  const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();

  const handleSubmit = () => {
    if (value.trim()) {
      editor.chain().focus().setLink({ href: value }).run();
      setValue("");
      onClose();
    }
  };
  return (
    <>
      <Button
        size={size}
        radius={radius}
        isIconOnly
        color={editor.isActive("link") ? activeColor : inactiveColor}
        variant={editor.isActive("link") ? activeVariant : inactiveVariant}
        onPress={() => {
          if (editor.isActive("link")) {
            editor.chain().focus().unsetLink().run();
          } else {
            onOpen();
          }
        }}
      >
        <Link className={cn("size-5", iconClassName)} />
      </Button>

      <Modal isOpen={isOpen} onOpenChange={onOpenChange} placement="center">
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h3 className="text-lg font-semibold">وارد کردن لینک</h3>
            <p className="text-sm text-default-500">
              لطفا آدرس لینک را وارد کنید
            </p>
          </ModalHeader>
          <ModalBody>
            <Input
              value={value}
              onValueChange={setValue}
              placeholder="https://example.com"
              variant="bordered"
              radius="sm"
              classNames={{
                label: "text-foreground-600 font-medium",
                input: "text-foreground-700",
              }}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleSubmit();
                }
              }}
              endContent={
                <Link className="pointer-events-none flex-shrink-0 text-foreground-400" />
              }
            />
          </ModalBody>
          <ModalFooter>
            <Button
              variant="light"
              onPress={() => {
                setValue("");
                onClose();
              }}
              radius="sm"
            >
              انصراف
            </Button>
            <Button
              color="primary"
              onPress={handleSubmit}
              radius="sm"
              isDisabled={!value.trim()}
            >
              تایید
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
};

LinkButton.propTypes = buttonPropTypes;

export const UndoButton = ({
  editor,
  size = "sm",
  radius,
  activeColor = "primary",
  inactiveColor = "default",
  activeVariant = "flat",
  inactiveVariant = "light",
  iconClassName,
}) => {
  return (
    <Button
      size={size}
      radius={radius}
      isIconOnly
      color={editor.isActive("undo") ? activeColor : inactiveColor}
      variant={editor.isActive("undo") ? activeVariant : inactiveVariant}
      isDisabled={!editor.can().chain().focus().undo().run()}
      onPress={() => {
        editor.chain().focus().undo().run();
      }}
    >
      <ArrowForward className={cn("size-5", iconClassName)} />
    </Button>
  );
};

UndoButton.propTypes = buttonPropTypes;

export const RedoButton = ({
  editor,
  size = "sm",
  radius,
  activeColor = "primary",
  inactiveColor = "default",
  activeVariant = "flat",
  inactiveVariant = "light",
  iconClassName,
}) => {
  return (
    <Button
      size={size}
      radius={radius}
      isIconOnly
      color={editor.isActive("redo") ? activeColor : inactiveColor}
      variant={editor.isActive("redo") ? activeVariant : inactiveVariant}
      isDisabled={!editor.can().chain().focus().redo().run()}
      onPress={() => {
        editor.chain().focus().redo().run();
      }}
    >
      <Back className={cn("size-5", iconClassName)} />
    </Button>
  );
};

RedoButton.propTypes = buttonPropTypes;
