import { Link } from 'react-router';
import useSidebarStore from '../../stores/useSidebarStore';
import { motion } from 'framer-motion';

const SidebarHeader = () => {
  const { isSidebarOpen } = useSidebarStore();
  return (
    <>
      <div className={'flex justify-center'}>
        <Link to={'/'} className='flex items-center gap-2'>
          <motion.div
            initial={{ opacity: 1 }}
            animate={!isSidebarOpen ? { opacity: 0, width: 0 } : {}}
            style={{ willChange: 'width' }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}>
            <img src='/images/logo-typography.png' className='max-h-9' />
          </motion.div>
          <img src='/images/patterns/baranPattern.png' className='max-h-9' />
        </Link>
      </div>
      <span className='mt-3 h-px bg-gradient-to-tr from-background via-foreground-100 to-background'></span>
    </>
  );
};

export default SidebarHeader;
