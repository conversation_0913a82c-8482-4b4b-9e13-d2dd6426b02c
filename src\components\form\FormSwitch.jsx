import { Switch, cn } from "@heroui/react";
import PropTypes from "prop-types";
import { use<PERSON><PERSON>roller } from "react-hook-form";

const FormSwitch = (props) => {
  const { control, name, className, label, ...other } = props;

  const {
    field: { onChange, ...field },
  } = useController({ name, control });

  return (
    <Switch
      {...field}
      onValueChange={onChange}
      className={cn("z-0", className)}
      {...other}
    >
      {label}
    </Switch>
  );
};

FormSwitch.propTypes = {
  name: PropTypes.string.isRequired,
  control: PropTypes.object.isRequired,
  className: PropTypes.string,
  color: PropTypes.string,
  defaultValue: PropTypes.bool,
  label: PropTypes.string,
  size: PropTypes.oneOf(["sm", "md", "lg"]),
};

export default FormSwitch;
