import { addToast, cn } from "@heroui/react";
import {
  parseAsInteger,
  parseAsString,
  useQueryState,
  useQueryStates,
} from "nuqs";
import PropTypes from "prop-types";
import { useEffect, useMemo, useState } from "react";

import ExportData from "./ExportData";
import PaginationData from "./PaginationData";
import TableData from "./TableData";
import CheckBoxFilter from "./filters/CheckBoxFilter";
import DateFilter from "./filters/DateFilter";
import RadioFilter from "./filters/RadioFilter";
import SearchFilter from "./filters/SearchFilter";
import SelectFilter from "./filters/SelectFilter";

const filterComponents = {
  search: SearchFilter,
  select: SelectFilter,
  radio: RadioFilter,
  checkbox: CheckBoxFilter,
  date: DateFilter,
};

/**
 * @param {ListDataProps} props
 */
const ListData = ({ columns, queryKey, features = {} }) => {
  const [page, setPage] = useQueryState("page", parseAsInteger.withDefault(1));

  const filterQueryStates = useMemo(() => {
    if (!features?.filter?.show || !features?.filter?.items) return {};
    return features.filter.items.reduce((acc, item) => {
      acc[item.name] = parseAsString;
      return acc;
    }, {});
  }, [features?.filter?.show, features?.filter?.items]);

  const [filterParams] = useQueryStates(filterQueryStates);

  const validFilterParams = useMemo(() => {
    return Object.entries(filterParams).reduce((acc, [key, value]) => {
      if (
        value !== "" &&
        value !== null &&
        value !== undefined &&
        value !== "null"
      ) {
        acc[key] = value;
      }
      return acc;
    }, {});
  }, [filterParams]);

  useEffect(() => {
    setPage(1);
  }, [filterParams]);

  // Get Table items Data
  const {
    data: _data,
    isLoading,
    error,
  } = queryKey.useQuery({
    variables: {
      query: {
        page,
        ...validFilterParams,
      },
    },
  });
  const data = useMemo(() => _data?.data, [_data]);

  useEffect(() => {
    if (_data?.status === false || error) {
      addToast({
        title: _data?.message ? _data?.message : "مشکلی پیش آمده است",
        color: "danger",
      });
    }
  }, [error, _data]);

  const [selectedRowKeys, setSelectedRowKeys] = useState(new Set([]));

  return (
    <div
      className={cn(
        "relative space-y-4 min-h-96",
        features?.wrapper?.className,
      )}
    >
      <div
        className={cn("flex items-start gap-4", features?.filter?.className)}
      >
        {features?.filter?.show &&
          features?.filter?.items &&
          features?.filter?.items.map((item) => {
            const FilterComponent = filterComponents[item.type];
            if (FilterComponent) {
              return (
                <FilterComponent
                  name={item.name}
                  key={item.name}
                  value={item?.value}
                  setValue={item?.setValue}
                  {...item.props}
                />
              );
            }
            return null;
          })}
        {features?.export?.show && (
          <ExportData
            data={data?.data}
            columns={columns}
            queryKey={queryKey}
            selectedRowKeys={selectedRowKeys}
            textButton={features?.export?.textButton}
            buttonProps={{
              className: cn(
                "ms-auto bg-background shrink-0",
                features?.export?.className,
              ),
              ...features?.export?.buttonProps,
            }}
          />
        )}
      </div>

      <TableData
        columns={columns}
        data={data?.data || []}
        pagination={data?.pagination || {}}
        selectionMode="multiple"
        selectedKeys={selectedRowKeys}
        onSelectionChange={setSelectedRowKeys}
        isLoading={isLoading}
        bottomContentPlacement="outside"
        bottomContent={
          <div className="py-2 px-2 flex justify-center items-center">
            {features?.pagination?.show && data?.pagination && (
              <PaginationData
                pagination={data?.pagination}
                isCompact
                showControls
                showShadow
                color="primary"
                {...features?.pagination?.props}
              />
            )}
          </div>
        }
        {...features?.table}
      />
    </div>
  );
};

ListData.propTypes = {
  columns: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      title: PropTypes.string.isRequired,
      field: PropTypes.oneOfType([PropTypes.string, PropTypes.func]).isRequired,
      type: PropTypes.string,
      exportable: PropTypes.bool,
    }),
  ).isRequired,
  queryKey: PropTypes.object.isRequired,
  features: PropTypes.shape({
    wrapper: PropTypes.shape({
      className: PropTypes.string,
    }),
    pagination: PropTypes.shape({
      show: PropTypes.bool,
      props: PropTypes.object,
    }),
    table: PropTypes.object,
    export: PropTypes.shape({
      show: PropTypes.bool,
      buttonProps: PropTypes.object,
    }),
    filter: PropTypes.shape({
      show: PropTypes.bool,
      items: PropTypes.arrayOf(
        PropTypes.shape({
          name: PropTypes.string.isRequired,
          type: PropTypes.oneOf([
            "search",
            "select",
            "radio",
            "checkbox",
            "date",
          ]),
          props: PropTypes.object,
        }),
      ),
    }),
  }),
};

export default ListData;
