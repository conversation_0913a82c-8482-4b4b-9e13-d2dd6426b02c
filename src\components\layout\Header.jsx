import { <PERSON><PERSON>, But<PERSON> } from "@heroui/react";
import { HamburgerMenu, Notification } from "iconsax-reactjs";
import PropTypes from "prop-types";
import useUserStore from "../../stores/useUserStore";
import Icon from "../icon/Icon";

const Header = ({ toggleDrawer }) => {
  const { user } = useUserStore();

  return (
    <div className="flex items-center shadow-sm z-50 justify-between bg-background px-4 py-3">
      {/* Profile */}
      <div className="flex items-center gap-2 sm:gap-3 md:gap-4">
        <Button
          isIconOnly
          className="lg:hidden"
          onPress={toggleDrawer}
          type="button"
          variant="light"
        >
          <HamburgerMenu className="size-5 text-foreground sm:size-6 md:size-7" />
        </Button>
        <Avatar
          className="size-9 md:size-10"
          src={user?.avatar || "/images/user.png"}
        />
        <div className="mt-1 flex items-center gap-1 text-sm font-semibold sm:text-base md:text-lg">
          <span className="text-primary">{user?.fullname || "بدون نام"}</span>
          <span className="text-foreground">، خوش آمدید!</span>
        </div>
      </div>
      {/* Actions */}
      {/* <div className='flex items-center gap-2 sm:gap-3 md:gap-4'>
        <Button isIconOnly type='button' variant='light'>
          <Icon
            name={'headset'}
            className={'size-5 text-foreground sm:size-6 md:size-7'}
          />
        </Button>
        <Button isIconOnly type='button' variant='light'>
          <Notification className='size-5 text-foreground sm:size-6 md:size-7' />
        </Button>
      </div> */}
    </div>
  );
};

Header.propTypes = {
  toggleDrawer: PropTypes.func.isRequired,
};
export default Header;
