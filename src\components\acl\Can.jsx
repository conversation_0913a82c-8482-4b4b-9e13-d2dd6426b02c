import PropTypes from 'prop-types';
import useAuthStore from '../../stores/useAuthStore';

const Can = ({ perm, children }) => {
  const permissions = useAuthStore((state) => state.permissions);

  if (!permissions || !Array.isArray(permissions)) {
    return null;
  }
  const hasAllPermissions = Array.isArray(perm)
    ? perm.every((p) => permissions.includes(p))
    : permissions.includes(perm);

  return hasAllPermissions ? children : null;
};

Can.propTypes = {
  perm: PropTypes.oneOfType([PropTypes.string, PropTypes.array]).isRequired,
  children: PropTypes.node.isRequired,
};

export default Can;
