import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  addToast,
} from "@heroui/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { Call, Eye, EyeSlash, Lock1, UserSquare } from "iconsax-reactjs";
import PropTypes from "prop-types";
import { useState } from "react";
import { useForm } from "react-hook-form";
import z from "zod";
import api from "../../../api";
import FormInput from "../../../components/form/FormInput";

const schema = z.object({
  first_name: z.string().min(3, "نام باید حداقل 3 کاراکتر باشد"),
  last_name: z.string().min(3, "نام خانوادگی باید حداقل 3 کاراکتر باشد"),
  mobile: z
    .string()
    .regex(/^\d+$/, "لطفا فقط عدد وارد کنید")
    .min(11, "شماره تلفن باید حداقل ۱۱ رقم باشد")
    .max(11, "شماره تلفن باید دقیقاً ۱۱ رقم باشد")
    .regex(/^09[0-9]{9}$/, "شماره تلفن معتبر نیست"),
  password: z.string().min(8, "رمز عبور باید حداقل ۸ کاراکتر باشد"),
});

const CreateAdminModal = ({ isOpen, onOpenChange, onClose }) => {
  const [isVisible, setIsVisible] = useState(false);
  const toggleVisibility = () => setIsVisible(!isVisible);

  // Create admin Form
  const { control, handleSubmit, reset } = useForm({
    resolver: zodResolver(schema),
    mode: "onChange",
    defaultValues: {
      first_name: "",
      last_name: "",
      mobile: "",
      password: "",
    },
  });

  const { mutate } = api.Entities.add.useMutation({
    onSuccess: (data) => {
      addToast({
        title: data?.message,
        variant: "solid",
        color: data?.status ? "success" : "danger",
      });
      reset();
      onClose();
    },
    onError: () => {
      addToast({
        title: "مشکلی پیش آمده است، مجددا تلاش کنید",
        color: "danger",
      });
    },
  });

  const onSubmit = (data) => {
    mutate(data);
  };

  return (
    <Modal
      size="lg"
      onClose={() => {
        reset();
      }}
      isOpen={isOpen}
      placement="center"
      className="mx-4"
      onOpenChange={onOpenChange}
    >
      <ModalContent className="rounded-small px-2 pb-5 pt-3 sm:px-4 md:px-6 md:pb-6 md:pt-4 lg:px-8 lg:pb-8">
        {() => (
          <>
            <ModalHeader className="flex flex-col gap-1" />
            <ModalBody className="gap flex flex-col items-center">
              <p className="text-lg font-semibold">ثبت درخواست</p>

              <FormInput
                control={control}
                name="first_name"
                inputProps={{
                  classNames: {
                    base: "mt-4",
                    input: "text-sm",
                    inputWrapper:
                      "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                  },
                  size: "lg",
                  radius: "full",
                  placeholder: "نام",
                  startContent: <UserSquare className="size-6 text-primary" />,
                }}
              />
              <FormInput
                control={control}
                name="last_name"
                inputProps={{
                  classNames: {
                    base: "mt-4",
                    input: "text-sm",
                    inputWrapper:
                      "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                  },
                  size: "lg",
                  radius: "full",
                  placeholder: "نام خانوادگی",
                  startContent: <UserSquare className="size-6 text-primary" />,
                }}
              />
              <FormInput
                control={control}
                name="mobile"
                inputProps={{
                  classNames: {
                    base: "mt-4",
                    input: "text-sm",
                    inputWrapper:
                      "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                  },
                  size: "lg",
                  radius: "full",
                  placeholder: "موبایل",
                  startContent: <Call className="size-6 text-primary" />,
                }}
              />
              <FormInput
                control={control}
                name="password"
                inputProps={{
                  type: isVisible ? "text" : "password",
                  classNames: {
                    base: "mt-4",
                    input: "text-sm",
                    inputWrapper:
                      "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                  },
                  size: "lg",
                  radius: "full",
                  placeholder: "رمز خود را وارد کنید",
                  startContent: <Lock1 className="size-6 text-primary" />,
                  endContent: (
                    <button
                      className="focus:outline-none"
                      type="button"
                      onClick={toggleVisibility}
                    >
                      {isVisible ? (
                        <EyeSlash className="pointer-events-none size-5 text-foreground-400" />
                      ) : (
                        <Eye className="pointer-events-none size-5 text-foreground-400" />
                      )}
                    </button>
                  ),
                }}
              />
            </ModalBody>
            <ModalFooter>
              <Button
                color="primary"
                radius="full"
                fullWidth
                size="lg"
                className="text-base font-medium"
                type="submit"
                onPress={handleSubmit(onSubmit)}
              >
                تائید
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};

CreateAdminModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onOpenChange: PropTypes.func.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default CreateAdminModal;
