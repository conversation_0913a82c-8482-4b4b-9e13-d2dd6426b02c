import { Button, addToast } from "@heroui/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQueryClient } from "@tanstack/react-query";
import { Lock1 } from "iconsax-reactjs";
import { EyeSlash } from "iconsax-reactjs";
import { Eye } from "iconsax-reactjs";
import Cookies from "js-cookie";
import PropTypes from "prop-types";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router";
import { z } from "zod";
import api from "../../api";
import FormInput from "../../components/form/FormInput";
import useUserStore from "../../stores/useUserStore";

const TOKEN_KEY = import.meta.env.VITE_TOKEN_KEY;

const schema = z.object({
  code: z.string().min(8, "رمز عبور باید حداقل ۸ کاراکتر باشد"),
});
const PasswordStep = ({ mobile, setStep }) => {
  // User Store
  const { setUser } = useUserStore();

  const navigate = useNavigate();

  const queryClient = useQueryClient();

  // Form
  const { control, handleSubmit, reset } = useForm({
    mode: "onChange",
    resolver: zodResolver(schema),
    defaultValues: {
      code: "",
    },
  });

  // Verify Password Mutation
  const { mutate, isPending } = api.Auth.verify.useMutation({
    onSuccess: async (data) => {
      addToast({
        title: "ورود با موفقیت انجام شد",
        color: "success",
      });
      Cookies.set(TOKEN_KEY, data.data?.token);
      setUser(data?.data?.user);
      await queryClient.refetchQueries({
        queryKey: api.Auth.me.getKey(),
      });
      navigate("/", { replace: true });
    },
    onError: ({ response: { status }, data }) => {
      if (status === 400) {
        addToast({
          title: data?.message,
          color: "danger",
        });
        reset();
      } else {
        addToast({
          title: "مشکلی پیش آمده است",
          description: "لطفا مجددا تلاش کنید",
          color: "danger",
        });
      }
    },
  });

  // Password Visibility
  const [isVisible, setIsVisible] = useState(false);
  const toggleVisibility = () => setIsVisible(!isVisible);

  return (
    <>
      <div className="flex gap-2 text-center font-medium">
        رمز خود را وارد کنید
        <span
          onClick={() => {
            reset();
            setStep("mobile");
          }}
          className="cursor-pointer text-primary"
        >
          ویرایش شماره موبایل
        </span>
      </div>

      <FormInput
        control={control}
        name="code"
        inputProps={{
          type: isVisible ? "text" : "password",
          classNames: {
            base: "mt-6",
            input: "text-sm",
            inputWrapper:
              "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
          },
          size: "lg",
          radius: "full",
          placeholder: "رمز خود را وارد کنید",
          startContent: <Lock1 className="size-6 text-primary" />,
          onKeyDown: (e) => {
            if (e.key === "Enter") {
              e.preventDefault();
              handleSubmit((data) => {
                mutate({ ...data, mobile: mobile });
              })();
            }
          },
          endContent: (
            <button
              className="focus:outline-none"
              type="button"
              onClick={toggleVisibility}
            >
              {isVisible ? (
                <EyeSlash className="pointer-events-none size-5 text-foreground-400" />
              ) : (
                <Eye className="pointer-events-none size-5 text-foreground-400" />
              )}
            </button>
          ),
        }}
      />

      <Button
        onPress={handleSubmit((data) => {
          mutate({ ...data, mobile: mobile });
        })}
        fullWidth
        isLoading={isPending}
        type="submit"
        color="primary"
        className="mt-6 font-semibold"
        size="lg"
        radius="full"
      >
        ارسال
      </Button>
    </>
  );
};

PasswordStep.propTypes = {
  mobile: PropTypes.string.isRequired,
  setStep: PropTypes.func.isRequired,
};

export default PasswordStep;
