import { Badge, Button, cn } from '@heroui/react';
import { Filter } from 'iconsax-reactjs';
import PropTypes from 'prop-types';

const FilterButton = ({
  onOpen,
  activeFilters = 0,
  BadgeColor = 'primary',
  className,
  variant = 'flat',
  size,
  radius = 'sm',
  ...other
}) => {
  const activeFiltersCount = Object.keys(activeFilters).filter(
    (key) => activeFilters[key]?.length,
  ).length;

  return (
    <Button
      variant={variant}
      radius={radius}
      className={cn('h-9 min-h-2 w-max py-0', className)}
      onPress={onOpen}
      size={size}
      endContent={
        activeFiltersCount > 0 ? (
          <Badge
            color={BadgeColor}
            className='size-3 min-h-2 min-w-2 border-none text-xs'
            content={activeFiltersCount}>
            <Filter className='size-5' />
          </Badge>
        ) : (
          <Filter className='size-5' />
        )
      }
      {...other}>
      فیلترها
    </Button>
  );
};

FilterButton.propTypes = {
  onOpen: PropTypes.func.isRequired,
  activeFilters: PropTypes.object,
  radius: PropTypes.oneOf(['sm', 'md', 'lg', 'full', 'none']),
  color: PropTypes.oneOf([
    'primary',
    'secondary',
    'success',
    'warning',
    'danger',
  ]),
  BadgeColor: PropTypes.oneOf([
    'primary',
    'secondary',
    'success',
    'warning',
    'danger',
  ]),
  className: PropTypes.string,
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  variant: PropTypes.oneOf([
    'light',
    'solid',
    'ghost',
    'flat',
    'bordered',
    'faded',
    'shadow',
  ]),
};

export default FilterButton;
