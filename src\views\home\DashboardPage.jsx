import {
  Avatar,
  But<PERSON>,
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  addToast,
  useDisclosure,
} from "@heroui/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { Edit2 } from "iconsax-reactjs";
import { useMemo } from "react";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Link } from "react-router";
import { z } from "zod";
import api from "../../api";
import MultiChartCard from "../../components/charts/MultiChartCard";
import PieChartCard from "../../components/charts/PieChartCard";
import FormInput from "../../components/form/FormInput";
import FormTextarea from "../../components/form/FormTextarea";
import convertDate from "../../utils/convertDate";

const schema = z.object({
  subject: z.string().min(3, "عنوان باید حداقل 3 کاراکتر باشد"),
  description: z.string().min(5, "متن باید حداقل 5 کاراکتر باشد"),
});

const DashboardPage = () => {
  // Disclosure for ticket modal
  const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();

  const { control, handleSubmit, reset } = useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      subject: "",
      description: "",
    },
  });

  // Ticket Mutation
  const { mutate } = api.Tickets.add.useMutation({
    onSuccess: (data) => {
      addToast({
        title: data?.message,
        variant: "solid",
        color: data?.status ? "success" : "danger",
      });
      reset();
      onClose();
    },
    onError: () => {
      addToast({
        title: "مشکلی پیش آمده است، مجددا تلاش کنید",
        color: "danger",
      });
    },
  });

  // Get Dashboard Data
  const { data: _data, isLoading, error } = api.Reports.detail.useQuery();
  const data = useMemo(() => _data?.data, [_data]);

  useEffect(() => {
    if (_data?.status === false || error) {
      addToast({
        title: _data?.message ? _data?.message : "مشکلی پیش آمده است",
        color: "danger",
      });
    }
  }, [error, _data]);

  return (
    <section className="flex flex-col gap-4 px-4 py-8 sm:px-6 lg:px-8">
      <div className="flex flex-col gap-6 rounded-small bg-background px-6 pb-7 pt-4 text-sm sm:text-base md:px-7 md:pb-8 lg:px-8 lg:pb-9 lg:pt-5">
        <div className="flex flex-wrap items-center justify-between gap-2">
          {!isLoading && (
            <div className="flex items-center gap-3">
              <Avatar
                className="size-10 shrink-0 sm:size-11"
                src={data?.organization?.image ?? "/images/user.png"}
              />
              <h2 className="font-medium">{data?.organization?.name}</h2>
            </div>
          )}
          {isLoading && (
            <div className="flex items-center gap-3">
              <Skeleton className="size-10 shrink-0 rounded-full sm:size-11" />
              <Skeleton className="h-4 w-32 rounded-lg" />
            </div>
          )}
          <div
            onClick={onOpen}
            className="cursor-pointer font-medium text-primary"
          >
            به پشتیبانی نیاز دارید؟
          </div>
        </div>
        {/* Ticket Modal */}
        <Modal
          size="lg"
          onClose={() => {
            reset();
          }}
          isOpen={isOpen}
          placement="center"
          className="mx-4"
          onOpenChange={onOpenChange}
        >
          <ModalContent className="rounded-small px-2 pb-5 pt-3 sm:px-4 md:px-6 md:pb-6 md:pt-4 lg:px-8 lg:pb-8">
            {() => (
              <>
                <ModalHeader className="flex flex-col gap-1" />
                <ModalBody className="gap flex flex-col items-center">
                  <p className="text-lg font-semibold">ثبت درخواست</p>

                  <FormInput
                    control={control}
                    name="subject"
                    inputProps={{
                      classNames: {
                        base: "mt-4",
                        input: "text-sm",
                        inputWrapper:
                          "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                      },
                      size: "lg",
                      radius: "full",
                      placeholder: "عنوان درخواست",
                      startContent: <Edit2 className="size-6 text-primary" />,
                    }}
                  />
                  <FormTextarea
                    control={control}
                    name="description"
                    textareaProps={{
                      classNames: {
                        base: "mt-3",
                        input: "text-sm",
                        inputWrapper:
                          "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                      },
                      size: "lg",
                      radius: "full",
                      placeholder: "متن درخواست",
                      minRows: 7,
                      startContent: <Edit2 className="size-6 text-primary" />,
                    }}
                  />
                </ModalBody>
                <ModalFooter>
                  <Button
                    color="primary"
                    radius="full"
                    fullWidth
                    size="lg"
                    className="text-base font-medium"
                    type="submit"
                    onPress={handleSubmit(mutate)}
                  >
                    تائید
                  </Button>
                </ModalFooter>
              </>
            )}
          </ModalContent>
        </Modal>
        {/* Rows */}
        <div className="flex flex-col items-start gap-x-4 gap-y-5 sm:flex-row sm:flex-wrap">
          <div className="flex w-full shrink-0 items-center justify-between whitespace-nowrap font-medium sm:basis-1/6 sm:justify-normal sm:gap-6 md:w-fit">
            {!isLoading && (
              <>
                <p className="text-foreground-400">تاریخ شروع قرارداد:</p>
                <p className="">{convertDate([2025, 5, 29]) || "ـ"}</p>
              </>
            )}

            {isLoading && <Skeleton className="h-4 w-full rounded-lg" />}
          </div>
          <div className="flex w-full shrink-0 items-center justify-between whitespace-nowrap font-medium sm:basis-1/6 sm:justify-normal sm:gap-6 md:w-fit">
            {!isLoading && (
              <>
                <p className="text-foreground-400">تعداد کل زبان آموز ها:</p>
                <p className="">
                  {data?.statistics?.students
                    ? `${data?.statistics?.students} نفر `
                    : "ـ"}
                </p>
              </>
            )}
            {isLoading && <Skeleton className="h-4 w-full rounded-lg" />}
          </div>
          <div className="flex w-full shrink-0 items-center justify-between whitespace-nowrap font-medium sm:basis-1/6 sm:justify-normal sm:gap-6 md:w-fit">
            {!isLoading && (
              <>
                <p className="text-foreground-400">تعداد زبان آموز های فعال:</p>
                <p className="">
                  {data?.statistics?.active_students
                    ? `${data?.statistics?.active_students} نفر `
                    : "ـ"}
                </p>
              </>
            )}
            {isLoading && <Skeleton className="h-4 w-full rounded-lg" />}
          </div>
          <div className="flex w-full shrink-0 items-center justify-between whitespace-nowrap font-medium sm:basis-1/6 sm:justify-normal sm:gap-6 md:w-fit">
            {!isLoading && (
              <>
                <p className="text-foreground-400">مجموع جلسات برگزار شده</p>
                <p className="">
                  {data?.statistics?.passd_maps
                    ? `${data?.statistics?.passd_maps} جلسه `
                    : "ـ"}
                </p>
              </>
            )}
            {isLoading && <Skeleton className="h-4 w-full rounded-lg" />}
          </div>
          <div className="flex w-full shrink-0 items-center justify-between whitespace-nowrap font-medium sm:basis-1/6 sm:justify-normal sm:gap-6 md:w-fit">
            {!isLoading && (
              <>
                <p className="text-foreground-400">مجموع ساعات آموزشی: </p>
                <p className="">
                  {data?.statistics?.total_learning_hours
                    ? `${data?.statistics?.total_learning_hours} ساعت `
                    : "ـ"}
                </p>
              </>
            )}
            {isLoading && <Skeleton className="h-4 w-full rounded-lg" />}
          </div>
          <div className="flex w-full shrink-0 items-center justify-between whitespace-nowrap font-medium sm:basis-1/6 sm:justify-normal sm:gap-6 md:w-fit">
            {!isLoading && (
              <>
                <p className="text-foreground-400">سرویس ها:</p>
                <p className="">
                  {data?.organization?.services
                    ? data?.organization?.services
                    : "ـ"}
                </p>
              </>
            )}
            {isLoading && <Skeleton className="h-4 w-full rounded-lg" />}
          </div>
          {/* <div className='flex w-full shrink-0 items-center justify-between whitespace-nowrap font-medium sm:basis-1/6 sm:justify-normal sm:gap-6 md:w-fit'>
            <p className='text-foreground-400'>میانگین NPS:</p>
            <p className=''>5.98</p>
          </div> */}
        </div>
      </div>

      <div className="items-stretch grid grid-cols-1 sm:grid-cols-4 h-full gap-4">
        <PieChartCard
          data={data?.charts?.absence_maps || []}
          isLoading={isLoading}
          title={"نسبت حضور به غیاب زبان آموزها"}
          pies={["secondary", "primary", "danger"]}
          className={
            "xl:col-span-1 relative col-span-1 overflow-hidden sm:col-span-2"
          }
        >
          <div className="absolute bg-background-50/60 gap-3 backdrop-blur-md z-10 flex flex-col px-4 py-6 items-center justify-end inset-0">
            <p className="text-foreground-900 font-medium">
              برای مشاهده ی نمودار عملکرد کلید کنید
            </p>
            <Button
              variant="bordered"
              fullWidth
              className="max-w-40 font-medium "
              as={Link}
              to={"/reports"}
              color="secondary"
              radius="full"
            >
              مشاهده عملکرد
            </Button>
          </div>
        </PieChartCard>

        <PieChartCard
          data={data?.charts?.active_students || []}
          isLoading={isLoading}
          title={"نسبت زبان‌آموز‌های فعال به غیر‌فعال"}
          pies={["secondary", "primary", "danger"]}
          className={
            "xl:col-span-1 col-span-1 overflow-hidden sm:col-span-2 relative "
          }
        >
          <div className="absolute bg-background-50/60 gap-3 backdrop-blur-md z-10 flex flex-col px-4 py-6 items-center justify-end inset-0">
            <p className="text-foreground-900 font-medium">
              برای مشاهده ی نمودار عملکرد کلید کنید
            </p>
            <Button
              variant="bordered"
              as={Link}
              to={"/reports"}
              fullWidth
              className="max-w-40 font-medium "
              color="secondary"
              radius="full"
            >
              مشاهده عملکرد
            </Button>
          </div>
        </PieChartCard>
        <MultiChartCard
          isLoading={isLoading}
          title={"گزارش ساعات جلسات برگزار شده"}
          data={data?.charts?.session_times || []}
          charts={[
            {
              dataKey: "value",
              color: "primary",
              title: "ساعت",
            },
          ]}
          className={
            "sm:col-span-4 relative overflow-hidden col-span-1 xl:col-span-2"
          }
          type={"bar"}
        >
          <div className="absolute bg-background-50/60 gap-3 backdrop-blur-md z-10 flex flex-col px-4 py-6 items-center justify-end inset-0">
            <p className="text-foreground-900 font-medium">
              برای مشاهده ی نمودار عملکرد کلید کنید
            </p>
            <Button
              variant="bordered"
              fullWidth
              as={Link}
              to={"/reports"}
              className="max-w-40 font-medium "
              color="secondary"
              radius="full"
            >
              مشاهده عملکرد
            </Button>
          </div>
        </MultiChartCard>
      </div>
    </section>
  );
};

export default DashboardPage;
