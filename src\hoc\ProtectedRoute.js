import { Navigate, Outlet } from 'react-router';
import useAuthStore from '../stores/useAuthStore';

const ProtectedRoute = (Comp, per, type = 'component') => {
  const WrappedComp = (props) => {
    const hasPermission = useAuthStore((state) =>
      state.permissions.includes(per),
    );
    if (!hasPermission) {
      return <Navigate to={'/unauthorized'} replace />;
    }
    if (type === 'router') {
      return <Outlet />;
    }
    return <Comp {...props} />;
  };
  return WrappedComp;
};

export default ProtectedRoute;
