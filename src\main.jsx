import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./styles/index.scss";
import { HeroUIProvider, ToastProvider } from "@heroui/react";
import { QueryClientProvider } from "@tanstack/react-query";
import { RouterProvider } from "react-router";
import { queryClient, router } from "./routers/router.jsx";
import "simplebar-react/dist/simplebar.min.css";
import { NuqsAdapter } from "nuqs/adapters/react";

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <HeroUIProvider locale="fa-IR">
        <ToastProvider
          placement="bottom-left"
          toastProps={{
            variant: "solid",
            classNames: {
              title: "text-base text-background ",
              base: "text-background",
              description: "text-background-200",
            },
          }}
        />
        <NuqsAdapter>
          <RouterProvider router={router} />
        </NuqsAdapter>
      </HeroUIProvider>
    </QueryClientProvider>
  </StrictMode>,
);
