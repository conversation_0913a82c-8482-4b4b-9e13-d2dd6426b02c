// Sample
// /**
//  * @typedef {'primary'|'secondary'|'success'|'warning'|'danger'} ColorVariant
//  * @typedef {'sm'|'md'|'lg'|'full'|'none'} RadiusVariant
//  * @typedef {'sm'|'md'|'lg'} SizeVariant
//  * @typedef {'light'|'solid'|'ghost'|'flat'|'bordered'|'faded'|'shadow'} ButtonVariant
//  */

/**
 * @typedef {object} FormCheckboxGroupProps
 * @property {string} name
 * @property {object} control
 * @property {function} renderItem
 * @property {object[]} items
 * @property {CheckboxGroupProps} [checkboxGroupProps]
 *
 * @typedef {CheckboxProps & {control: object, name: string, wrapperClassName: string}} FormCheckboxProps
 *
 * @typedef {object} TableDataProps
 * @property {object[]} columns
 * @property {ReactNode} emptyContent
 * @property {object[]} data
 * @property {boolean} isLoading
 * @property {boolean} readOnly
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'auto' | 'fixed'} [layout='auto']
 * @property {'none' | 'sm' | 'md' | 'lg'} [radius='lg']
 * @property {'none' | 'sm' | 'md' | 'lg'} [shadow='sm']
 * @property {number} [maxTableHeight=600]
 * @property {number} [rowHeight=40]
 * @property {boolean} [isVirtualized]
 * @property {boolean} [hideHeader=false]
 * @property {boolean} [isStriped=false]
 * @property {boolean} [isCompact=false]
 * @property {boolean} [isHeaderSticky=false]
 * @property {boolean} [fullWidth=true]
 * @property {boolean} [removeWrapper=false]
 * @property {React.ComponentType<any>} [BaseComponent='div']
 * @property {ReactNode} [topContent]
 * @property {ReactNode} [bottomContent]
 * @property {'inside' | 'outside'} [topContentPlacement='inside']
 * @property {'inside' | 'outside'} [bottomContentPlacement='inside']
 * @property {boolean} [showSelectionCheckboxes]
 * @property {SortDescriptor} [sortDescriptor]
 * @property {Selection} [selectedKeys]
 * @property {Selection} [defaultSelectedKeys]
 * @property {Selection} [disabledKeys]
 * @property {boolean} [disallowEmptySelection]
 * @property {'single' | 'multiple' | 'none'} [selectionMode='none']
 * @property {'toggle' | 'replace'} [selectionBehavior='toggle']
 * @property {'selection' | 'all'} [disabledBehavior='selection']
 * @property {boolean} [allowDuplicateSelectionEvents]
 * @property {boolean} [disableAnimation=false]
 * @property {CheckboxProps} [checkboxesProps]
 * @property {Partial<Record<'base' | 'table' | 'thead' | 'tbody' | 'tfoot' | 'emptyWrapper' | 'loadingWrapper' | 'wrapper' | 'tr' | 'th' | 'td' | 'sortIcon', string>>} [classNames]
 * @property {boolean} [isKeyboardNavigationDisabled=false]
 *
 *
 * @typedef {object} PaginationDataProps
 * @property {'flat' | 'bordered' | 'light' | 'faded'} [variant='flat']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius='xl']
 * @property {number} [total=1]
 * @property {number} [dotsJump=5]
 * @property {number} [initialPage=1]
 * @property {number} [page]
 * @property {number} [siblings=1]
 * @property {number} [boundaries=1]
 * @property {boolean} [loop=false]
 * @property {boolean} [isCompact=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [showShadow=false]
 * @property {boolean} [showControls=false]
 * @property {boolean} [disableCursorAnimation=false]
 * @property {boolean} [disableAnimation=false]
 * @property {PaginationItemProps} [renderItem]
 * @property {(page: string) => string} [getItemAriaLabel]
 * @property {Partial<Record<'base' | 'wrapper' | 'prev' | 'next' | 'item' | 'cursor' | 'forwardIcon' | 'ellipsis' | 'chevronNext', string>>} [classNames]
 *
 *
 * @typedef {object} SearchFilterItem
 * @property {'search'} type
 * @property {string} name
 * @property {SearchFilterProps} props
 *
 * @typedef {object} SelectFilterItem
 * @property {string} name
 * @property {'select'} type
 * @property {SelectFilterProps} props
 *
 * @typedef {object} DatePickerFilterItem
 * @property {string} name
 * @property {'date'} type
 * @property {DatePickerFilterProps & {isRange?: boolean}} props
 *
 * @typedef {SearchFilterItem | SelectFilterItem | DatePickerFilterItem} FilterItem
 *
 * @typedef {object} ListDataProps
 * @property {object[]} columns
 * @property {string} columns.id
 * @property {string} columns.title
 * @property {(string|function)} columns.field
 * @property {string} columns.type
 * @property {boolean} [columns.exportable=true] - Indicates if the column can be exported to Excel. Defaults to true.
 * @property {QueryKey} queryKey
 * @property {object} features
 * @property {object} [features.wrapper]
 * @property {string} [features.wrapper.className]
 * @property {object} [features.pagination]
 * @property {boolean} [features.pagination.show]
 * @property {PaginationDataProps} [features.pagination.props]
 * @property {TableDataProps} [features.table]
 * @property {object} [features.export]
 * @property {boolean} [features.export.show]
 * @property {ButtonProps} [features.export.buttonProps]
 * @property {string} [features.export.textButton]
 * @property {object} [features.filter]
 * @property {boolean} [features.filter.show]
 * @property {string} [features.filter.className]
 * @property {FilterItem[]} [features.filter.items]
 *
 *
 * @typedef {object} ButtonProps
 * @property {ReactNode} [children]
 * @property {'solid' | 'bordered' | 'light' | 'flat' | 'faded' | 'shadow' | 'ghost'} [variant='solid']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius]
 * @property {ReactNode} [startContent]
 * @property {ReactNode} [endContent]
 * @property {ReactNode} [spinner]
 * @property {'start' | 'end'} [spinnerPlacement='start']
 * @property {boolean} [fullWidth=false]
 * @property {boolean} [isIconOnly=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isLoading=false]
 * @property {boolean} [disableRipple=false]
 * @property {boolean} [disableAnimation=false]
 * @property {(e: PressEvent) => void} [onPress]
 * @property {(e: PressEvent) => void} [onPressStart]
 * @property {(e: PressEvent) => void} [onPressEnd]
 * @property {(isPressed: boolean) => void} [onPressChange]
 * @property {(e: PressEvent) => void} [onPressUp]
 * @property {(e: KeyboardEvent) => void} [onKeyDown]
 * @property {(e: KeyboardEvent) => void} [onKeyUp]
 *
 *
 * @typedef {object} ExportDataProps
 * @property {object[]} data
 * @property {object[]} columns
 * @property {QueryKey} queryKey
 * @property {string} textButton
 * @property {ButtonProps} buttonProps
 * @property {(string|object)} selectedRowKeys
 *
 * @typedef {object} InputProps
 * @property {ReactNode} [children]
 * @property {'flat' | 'bordered' | 'faded' | 'underlined'} [variant='flat']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius]
 * @property {ReactNode} [label]
 * @property {string} [value]
 * @property {string} [defaultValue]
 * @property {string} [placeholder]
 * @property {ReactNode} [description]
 * @property {ReactNode | ((v: ValidationResult) => ReactNode)} [errorMessage]
 * @property {(value: string) => ValidationError | true | null | undefined} [validate]
 * @property {'native' | 'aria'} [validationBehavior='native']
 * @property {number} [minLength]
 * @property {number} [maxLength]
 * @property {string} [pattern]
 * @property {'text' | 'email' | 'url' | 'password' | 'tel' | 'search' | 'file'} [type='text']
 * @property {ReactNode} [startContent]
 * @property {ReactNode} [endContent]
 * @property {'inside' | 'outside' | 'outside-left'} [labelPlacement='inside']
 * @property {boolean} [fullWidth=true]
 * @property {boolean} [isClearable=false]
 * @property {boolean} [isRequired=false]
 * @property {boolean} [isReadOnly=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isInvalid=false]
 * @property {RefObject<HTMLDivElement>} [baseRef]
 * @property {boolean} [disableAnimation=false]
 * @property {Partial<Record<'base' | 'label' | 'inputWrapper' | 'innerWrapper' | 'mainWrapper' | 'input' | 'clearButton' | 'helperWrapper' | 'description' | 'errorMessage', string>>} [classNames]
 * @property {(e: React.ChangeEvent<HTMLInputElement>) => void} [onChange]
 * @property {(value: string) => void} [onValueChange]
 * @property {() => void} [onClear]
 *
 *
 * @typedef {object} FilterComp
 * @property {string} name
 * @property {string} value
 * @property {(value: string) => void} setValue
 *
 *
 * @typedef {InputProps & FilterComp} SearchFilterProps
 *
 * @typedef {DatePickerProps & FilterComp} DatePickerFilterProps
 *
 *
 * @typedef {object} SelectProps
 * @property {ReactNode[]} children
 * @property {Iterable<T>} items
 * @property {'single' | 'multiple'} selectionMode
 * @property {'all' | Iterable<React.Key>} selectedKeys
 * @property {Iterable<React.Key>} disabledKeys
 * @property {'all' | Iterable<React.Key>} defaultSelectedKeys
 * @property {'flat' | 'bordered' | 'faded' | 'underlined'} [variant='flat']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} radius
 * @property {string} [placeholder='Select an option']
 * @property {'inside' | 'outside' | 'outside-left'} [labelPlacement='inside']
 * @property {ReactNode} label
 * @property {ReactNode} description
 * @property {ReactNode | ((v: ValidationResult) => ReactNode)} errorMessage
 * @property {ReactNode} startContent
 * @property {ReactNode} endContent
 * @property {ReactNode} selectorIcon
 * @property {React.RefObject<HTMLElement>} scrollRef
 * @property {React.RefObject<HTMLElement>} spinnerRef
 * @property {number} [maxListboxHeight='256']
 * @property {number} [itemHeight='32']
 * @property {boolean} isVirtualized
 * @property {boolean} [fullWidth=true]
 * @property {boolean} isOpen
 * @property {boolean} defaultOpen
 * @property {boolean} [isRequired=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isMultiline=false]
 * @property {boolean} [isInvalid=false]
 * @property {'valid' | 'invalid'} validationState
 * @property {boolean} [showScrollIndicators=true]
 * @property {boolean} [autoFocus=false]
 * @property {boolean} [disallowEmptySelection=false]
 * @property {boolean} [disableAnimation=true]
 * @property {boolean} [disableSelectorIconRotation=false]
 * @property {boolean} [hideEmptyContent=false]
 * @property {PopoverProps} popoverProps
 * @property {ListboxProps} listboxProps
 * @property {ScrollShadowProps} scrollShadowProps
 * @property {Partial<Record<"base" | "label" | "trigger" | "mainWrapper" | "innerWrapper" | "selectorIcon" | "value" | "listboxWrapper" | "listbox" | "popoverContent" | "helperWrapper" | "description" | "errorMessage", string>>} classNames
 * @property {() => void} onClose
 * @property {(isOpen: boolean) => void} onOpenChange
 * @property {(keys: "all" | Set<React.Key> & {anchorKey?: string; currentKey?: string}) => void} onSelectionChange
 * @property {React.ChangeEvent<HTMLSelectElement>} onChange
 * @property {RenderValueFunction} renderValue
 * @property {SelectItemProps} itemProps
 *
 * @typedef {SelectProps & FilterComp & {chipMode: boolean}} SelectFilterProps
 *
 * @typedef {object} SelectItemProps
 * @property {ReactNode} children
 * @property {React.Key} key
 * @property {string | ReactNode} title
 * @property {string} textValue
 * @property {string | ReactNode} description
 * @property {string | ReactNode} shortcut
 * @property {ReactNode} startContent
 * @property {ReactNode} endContent
 * @property {ListboxItemSelectedIconProps} selectedIcon
 * @property {string} href
 * @property {HTMLAttributeAnchorTarget} target
 * @property {string} rel
 * @property {boolean | string} download
 * @property {string} ping
 * @property {HTMLAttributeReferrerPolicy} referrerPolicy
 * @property {boolean} shouldHighlightOnFocus
 * @property {boolean} hideSelectedIcon
 * @property {boolean} showDivider
 * @property {boolean} isDisabled
 * @property {boolean} isSelected
 * @property {boolean} isReadOnly
 * @property {Partial<Record<"base" | "wrapper" | "title" | "description" | "shortcut" | "selectedIcon", string>>} classNames
 *
 *
 * @typedef {object} CalendarProps
 * @property {DateValue | null} value
 * @property {DateValue | null} defaultValue
 * @property {DateValue} minValue
 * @property {DateValue} maxValue
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {number} [visibleMonths=1]
 * @property {'sun' | 'mon' | 'tue' | 'wed' | 'thu' | 'fri' | 'sat'} firstDayOfWeek
 * @property {DateValue} focusedValue
 * @property {DateValue} defaultFocusedValue
 * @property {number | string} [calendarWidth='256']
 * @property {'single' | 'visible'} [pageBehavior='visible']
 * @property {'narrow' | 'short' | 'long' | undefined} [weekdayStyle='narrow']
 * @property {boolean} [showMonthAndYearPickers=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isReadOnly=false]
 * @property {boolean} isInvalid
 * @property {boolean} [autoFocus=false]
 * @property {boolean} showHelper
 * @property {boolean} showShadow
 * @property {boolean} isHeaderExpanded
 * @property {boolean} isHeaderDefaultExpanded
 * @property {ReactNode} topContent
 * @property {ReactNode} bottomContent
 * @property {(date: DateValue) => boolean} isDateUnavailable
 * @property {(calendar: SupportedCalendars) => Calendar | null} [createCalendar='all calendars']
 * @property {ReactNode | ((v: ValidationResult) => ReactNode)} errorMessage
 * @property {boolean} [hideDisabledDates=false]
 * @property {boolean} [disableAnimation=false]
 * @property {Partial<Record<'base' | 'prevButton' | 'nextButton' | 'headerWrapper' | 'header' | 'title' | 'content' | 'gridWrapper' | 'grid' | 'gridHeader' | 'gridHeaderRow' | 'gridHeaderCell' | 'gridBody' | 'gridBodyRow' | 'cell' | 'cellButton' | 'pickerWrapper' | 'pickerMonthList' | 'pickerYearList' | 'pickerHighlight' | 'pickerItem' | 'helperWrapper' | 'errorMessage', string>>} classNames
 * @property {(value: MappedDateValue) => void} onChange
 * @property {(date: CalendarDate) => void} onFocusChange
 * @property {(isExpanded: boolean) => void} onHeaderExpandedChange
 *
 *
 * @typedef {object} DatePickerProps
 * @property {ReactNode} label
 * @property {ZonedDateTime | CalendarDate | CalendarDateTime | undefined | null} value
 * @property {'flat' | 'bordered' | 'faded' | 'underlined'} [variant='flat']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} radius
 * @property {string} defaultValue
 * @property {ZonedDateTime | CalendarDate | CalendarDateTime | undefined | null} placeholderValue
 * @property {ReactNode} description
 * @property {ReactNode | ((v: ValidationResult) => ReactNode)} errorMessage
 * @property {(value: MappedDateValue<DateValue>) => ValidationError | true | null | undefined} validate
 * @property {'native' | 'aria'} [validationBehavior='native']
 * @property {ReactNode} startContent
 * @property {ReactNode} endContent
 * @property {'inside' | 'outside' | 'outside-left'} [labelPlacement='inside']
 * @property {boolean} [isRequired=false]
 * @property {boolean} [isReadOnly=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isInvalid=false]
 * @property {number} [visibleMonths=1]
 * @property {'sun' | 'mon' | 'tue' | 'wed' | 'thu' | 'fri' | 'sat'} firstDayOfWeek
 * @property {ReactNode} selectorIcon
 * @property {PageBehavior} [pageBehavior='visible']
 * @property {number} [calendarWidth=256]
 * @property {(date: DateValue) => boolean} isDateUnavailable
 * @property {boolean} [autoFocus=false]
 * @property {12 | 24} hourCycle
 * @property {'day' | 'hour' | 'minute' | 'second'} granularity
 * @property {boolean} hideTimeZone
 * @property {boolean} [shouldForceLeadingZeros=true]
 * @property {ReactNode} CalendarBottomContent
 * @property {boolean} [showMonthAndYearPickers=false]
 * @property {PopoverProps} [popoverProps={ placement: "bottom", triggerScaleOnOpen: false, offset: 13 }]
 * @property {ButtonProps} [selectorButtonProps={ size: "sm", variant: "light", radius: "full", isIconOnly: true }]
 * @property {CalendarProps} [calendarProps={ size: "sm", variant: "light", radius: "full", isIconOnly: true }]
 * @property {TimeInputProps} [timeInputProps={ size: "sm", variant: "light", radius: "full", isIconOnly: true }]
 * @property {boolean} [disableAnimation=false]
 * @property {Partial<Record<"base" | "selectorButton" | "selectorIcon" | "popoverContent" | "calendar" | "calendarContent" | "timeInputLabel" | "timeInput" | "label" | "inputWrapper" | "input" | "segment" | "helperWrapper" | "description" | "errorMessage", string>>} classNames
 * @property {(value: ZonedDateTime | CalendarDate | CalendarDateTime) => void} onChange
 * @property {(e: FocusEvent<HTMLInputElement>) => void} onFocus
 * @property {(e: FocusEvent<HTMLInputElement>) => void} onBlur
 * @property {(isFocused: boolean) => void} onFocusChange
 * @property {(e: KeyboardEvent) => void} onKeyDown
 * @property {(e: KeyboardEvent) => void} onKeyUp
 *
 *
 * @typedef {object} FormUploadAvatarProps
 * @property {string} name
 * @property {object} control
 * @property {object} [classNames]
 * @property {string} [classNames.wrapper]
 * @property {string} [classNames.avatar]
 * @property {string} [classNames.button]
 *
 *
 * @typedef {object} InputProps
 * @property {ReactNode} [children]
 * @property {'flat' | 'bordered' | 'faded' | 'underlined'} [variant='flat']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius]
 * @property {ReactNode} [label]
 * @property {string} [value]
 * @property {string} [defaultValue]
 * @property {string} [placeholder]
 * @property {ReactNode} [description]
 * @property {ReactNode | ((v: ValidationResult) => ReactNode)} [errorMessage]
 * @property {(value: string) => ValidationError | true | null | undefined} [validate]
 * @property {'native' | 'aria'} [validationBehavior='native']
 * @property {number} [minLength]
 * @property {number} [maxLength]
 * @property {string} [pattern]
 * @property {'text' | 'email' | 'url' | 'password' | 'tel' | 'search' | 'file'} [type='text']
 * @property {ReactNode} [startContent]
 * @property {ReactNode} [endContent]
 * @property {'inside' | 'outside' | 'outside-left'} [labelPlacement='inside']
 * @property {boolean} [fullWidth=true]
 * @property {boolean} [isClearable=false]
 * @property {boolean} [isRequired=false]
 * @property {boolean} [isReadOnly=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isInvalid=false]
 * @property {RefObject<HTMLDivElement>} [baseRef]
 * @property {boolean} [disableAnimation=false]
 * @property {Partial<Record<'base' | 'label' | 'inputWrapper' | 'innerWrapper' | 'mainWrapper' | 'input' | 'clearButton' | 'helperWrapper' | 'description' | 'errorMessage', string>>} [classNames]
 * @property {(e: React.ChangeEvent<HTMLInputElement>) => void} [onChange]
 * @property {(value: string) => void} [onValueChange]
 * @property {() => void} [onClear]
 *
 * @typedef {object} NumberInputProps
 * @property {ReactNode} [children]
 * @property {'flat' | 'bordered' | 'faded' | 'underlined'} [variant='flat']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius]
 * @property {string} name
 * @property {ReactNode} [label]
 * @property {ReactNode} [description]
 * @property {string} [value]
 * @property {string} [defaultValue]
 * @property {string} [placeholder]
 * @property {ReactNode | ((v: ValidationResult) => ReactNode)} [errorMessage]
 * @property {(value: string) => ValidationError | true | null | undefined} [validate]
 * @property {'native' | 'aria'} [validationBehavior='native']
 * @property {number} [minValue]
 * @property {number} [maxValue]
 * @property {Intl.NumberFormatOptions} [formatOptions]
 * @property {number} [step='1']
 * @property {boolean} [hideStepper]
 * @property {boolean} [isWheelDisabled]
 * @property {ReactNode} [startContent]
 * @property {ReactNode} [endContent]
 * @property {'inside' | 'outside' | 'outside-left'} [labelPlacement='inside']
 * @property {boolean} [fullWidth=true]
 * @property {boolean} [isClearable=false]
 * @property {boolean} [isRequired=false]
 * @property {boolean} [isReadOnly=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isInvalid=false]
 * @property {string} [incrementAriaLabel]
 * @property {string} [decrementAriaLabel]
 * @property {RefObject<HTMLDivElement>} [baseRef]
 * @property {boolean} [disableAnimation=false]
 * @property {Partial<Record<'base' | 'label' | 'inputWrapper' | 'innerWrapper' | 'mainWrapper' | 'input' | 'clearButton' | 'stepperButton' | 'helperWrapper' | 'stepperWrapper' | 'description' | 'errorMessage', string>>} [classNames]
 * @property {(e: React.ChangeEvent<HTMLInputElement>) => void} [onChange]
 * @property {(value: string) => void} [onValueChange]
 * @property {() => void} [onClear]
 *
 *
 * @typedef {object} FormInputNumberItem
 * @property {string} name
 * @property {object} control
 * @property {object} rules
 * @property {'number'} type
 * @property {NumberInputProps} inputProps
 *
 * @typedef {object} FormInputTextItem
 * @property {string} name
 * @property {object} control
 * @property {object} rules
 * @property {'text'} type
 * @property {InputProps} inputProps
 *
 * @typedef {object} FormTextareaProps
 * @property {string} name
 * @property {object} control
 * @property {object} rules
 * @property {TextareaProps} textareaProps
 *
 * @typedef {object} FormAutoCompleteProps
 * @property {string} name
 * @property {object[]} items
 * @property {function} renderItem
 * @property {object} control
 * @property {AutoCompleteProps} autoCompleteProps
 *
 * @typedef {object} FormRadioGroupProps
 * @property {string} name
 * @property {object} control
 * @property {function} renderItem
 * @property {object[]} items
 * @property {RadioGroupProps} radioGroupProps
 *
 * @typedef {FormInputNumberItem | FormInputTextItem} FormInputProps
 *
 * @typedef {DatePickerProps & {isRange?: boolean , control: object, name: string}} FormDatePickerProps
 *
 *
 * @typedef {object} TextareaProps
 * @property {ReactNode} [children]
 * @property {number} [minRows='3']
 * @property {number} [maxRows='8']
 * @property {boolean} [cacheMeasurements=false]
 * @property {'flat' | 'bordered' | 'faded' | 'underlined'} [variant='flat']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius]
 * @property {ReactNode} [label]
 * @property {string} [value]
 * @property {string} [defaultValue]
 * @property {string} [placeholder]
 * @property {ReactNode} [startContent]
 * @property {ReactNode} [endContent]
 * @property {ReactNode} [description]
 * @property {ReactNode | ((v: ValidationResult) => ReactNode)} [errorMessage]
 * @property {(value: string) => ValidationError | true | null | undefined} [validate]
 * @property {'native' | 'aria'} [validationBehavior='native']
 * @property {'inside' | 'outside' | 'outside-left'} [labelPlacement='inside']
 * @property {boolean} [fullWidth=true]
 * @property {boolean} [isRequired=false]
 * @property {boolean} [isReadOnly]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isClearable=false]
 * @property {boolean} [isInvalid=false]
 * @property {'valid' | 'invalid'} [validationState]
 * @property {boolean} [disableAutosize=false]
 * @property {boolean} [disableAnimation=false]
 * @property {Partial<Record<'base' | 'label' | 'inputWrapper' | 'innerWrapper' | 'input' | 'description' | 'errorMessage', string>>} [classNames]
 * @property {(e: React.ChangeEvent<HTMLInputElement>) => void} onChange
 * @property {(value: string) => void} onValueChange
 * @property {() => void} onClear
 * @property {(height: number, meta: { rowHeight: number }) => void} onHeightChange
 *
 *
 * @typedef {object} AutoCompleteProps
 * @property {ReactNode[]} children
 * @property {ReactNode} label
 * @property {string} name
 * @property {'flat' | 'bordered' | 'faded' | 'underlined'} [variant='flat']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} radius
 * @property {Iterable<T>} items
 * @property {Iterable<T>} defaultItems
 * @property {string} inputValue
 * @property {string} defaultInputValue
 * @property {boolean} [allowsCustomValue=false]
 * @property {boolean} [isLoading=false]
 * @property {boolean} [allowsEmptyCollection=true]
 * @property {boolean} [shouldCloseOnBlur=true]
 * @property {boolean} [multiple=false]
 * @property {string} placeholder
 * @property {ReactNode} description
 * @property {'focus' | 'input' | 'manual'} [menuTrigger='focus']
 * @property {'inside' | 'outside' | 'outside-left'} [labelPlacement='inside']
 * @property {React.Key} selectedKey
 * @property {React.Key} defaultSelectedKey
 * @property {'all' | React.Key[]} disabledKeys
 * @property {ReactNode | ((v: ValidationResult) => ReactNode)} errorMessage
 * @property {(value: { inputValue: string, selectedKey: React.Key }) => ValidationError | true | null | undefined} validate
 * @property {'native' | 'aria'} [validationBehavior='native']
 * @property {ReactNode} startContent
 * @property {ReactNode} endContent
 * @property {boolean} [autoFocus=false]
 * @property {(textValue: string, inputValue: string) => boolean} defaultFilter
 * @property {Intl.CollatorOptions} [filterOptions={ sensitivity: 'base'}]
 * @property {number} [maxListboxHeight=256]
 * @property {number} [itemHeight=32]
 * @property {boolean} [isVirtualized]
 * @property {boolean} [isReadOnly=false]
 * @property {boolean} [isRequired=false]
 * @property {boolean} [isInvalid=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [fullWidth=true]
 * @property {ReactNode} selectorIcon
 * @property {ReactNode} clearIcon
 * @property {boolean} [showScrollIndicators=true]
 * @property {React.RefObject<HTMLElement>} scrollRef
 * @property {InputProps} inputProps
 * @property {PopoverProps} popoverProps
 * @property {ListboxProps} listboxProps
 * @property {ScrollShadowProps} scrollShadowProps
 * @property {ButtonProps} selectorButtonProps
 * @property {ButtonProps} clearButtonProps
 * @property {boolean} [isClearable=true]
 * @property {boolean} [disableClearable=false]
 * @property {boolean} [disableAnimation=true]
 * @property {boolean} [disableSelectorIconRotation=false]
 * @property {Partial<Record<'base' | 'listboxWrapper' | 'listbox' | 'popoverContent' | 'endContentWrapper' | 'clearButton' | 'selectorButton', string>>} classNames
 * @property {(isOpen: boolean, menuTrigger?: MenuTriggerAction) => void} onOpenChange
 * @property {(value: string) => void} onInputChange
 * @property {(key: React.Key) => void} onSelectionChange
 * @property {(e: FocusEvent<HTMLInputElement>) => void} onFocus
 * @property {(e: FocusEvent<HTMLInputElement>) => void} onBlur
 * @property {(isFocused: boolean) => void} onFocusChange
 * @property {(e: KeyboardEvent) => void} onKeyDown
 * @property {(e: KeyboardEvent) => void} onKeyUp
 * @property {() => void} onClose
 * @property {() => void} onClear
 *
 *
 * @typedef {object} ListboxProps
 * @property {ReactNode[]} children
 * @property {Iterable<T>} items
 * @property {'solid' | 'bordered' | 'light' | 'flat' | 'faded' | 'shadow'} [variant='solid']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'none' | 'single' | 'multiple'} selectionMode
 * @property {React.Key[]} selectedKeys
 * @property {React.Key[]} disabledKeys
 * @property {'all' | React.Key[]} defaultSelectedKeys
 * @property {boolean} [disallowEmptySelection=false]
 * @property {boolean} [shouldHighlightOnFocus=false]
 * @property {boolean | 'first' | 'last'} [autoFocus=false]
 * @property {ReactNode} topContent
 * @property {ReactNode} bottomContent
 * @property {ReactNode} [emptyContent='No items.']
 * @property {boolean} [shouldFocusWrap=false]
 * @property {boolean} [isVirtualized=false]
 * @property {Record<'maxListboxHeight' & 'itemHeight', number>} virtualization
 * @property {boolean} [hideEmptyContent=false]
 * @property {boolean} [hideSelectedIcon=false]
 * @property {boolean} [disableAnimation=false]
 * @property {'toggle' | 'replace'} [selectionBehavior='toggle']
 * @property {Partial<Record<'base' | 'list' | 'emptyContent', string>>} classNames
 * @property {Partial<Record<'base' | 'wrapper' | 'title' | 'description' | 'selectedIcon', string>>} itemClasses
 * @property {(key: React.Key) => void} onAction
 * @property {(keys: React.Key[]) => void} onSelectionChange
 *
 *
 * @typedef {object} RadioGroupProps
 * @property {ReactNode | ReactNode[]} children
 * @property {ReactNode} label
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='primary']
 * @property {'horizontal' | 'vertical'} [orientation='vertical']
 * @property {string} name
 * @property {string[]} value
 * @property {string[]} defaultValue
 * @property {ReactNode} description
 * @property {ReactNode | ((v: ValidationResult) => ReactNode)} errorMessage
 * @property {(value: string) => ValidationError | true | null | undefined} validate
 * @property {'native' | 'aria'} [validationBehavior='native']
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isRequired=false]
 * @property {boolean} isReadOnly
 * @property {boolean} [isInvalid=false]
 * @property {'valid' | 'invalid'} validationState
 * @property {boolean} [disableAnimation=false]
 * @property {Partial<Record<'base' | 'wrapper' | 'label', string>>} classNames
 * @property {(e: React.ChangeEvent<HTMLInputElement>) => void} onChange
 * @property {(value: string) => void} onValueChange
 *
 *
 * @typedef {object} FormRichTextProps
 * @property {string} name
 * @property {object} control
 * @property {object} [enabledButtons]
 * @property {boolean} [enabledButtons.link=true]
 * @property {boolean} [enabledButtons.bold=true]
 * @property {boolean} [enabledButtons.italic=true]
 * @property {boolean} [enabledButtons.underline=true]
 * @property {boolean} [enabledButtons.strike=true]
 * @property {boolean} [enabledButtons.heading=true]
 * @property {boolean} [enabledButtons.alignment=true]
 * @property {boolean} [enabledButtons.unorderedList=true]
 * @property {boolean} [enabledButtons.orderedList=true]
 * @property {boolean} [enabledButtons.image=true]
 * @property {boolean} [enabledButtons.link=true]
 * @property {boolean} [enabledButtons.undo=true]
 * @property {boolean} [enabledButtons.redo=true]
 * @property {string} className
 *
 * @typedef {object} CheckboxProps
 * @property {ReactNode} children
 * @property {CheckboxIconProps} icon
 * @property {string} value
 * @property {string} name
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='primary']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} radius
 * @property {boolean} [lineThrough=false]
 * @property {boolean} isSelected
 * @property {boolean} defaultSelected
 * @property {boolean} [isRequired=false]
 * @property {boolean} isReadOnly
 * @property {boolean} [isDisabled=false]
 * @property {boolean} isIndeterminate
 * @property {boolean} [isInvalid=false]
 * @property {'valid' | 'invalid'} validationState
 * @property {boolean} [disableAnimation=false]
 * @property {Partial<Record<'base' | 'wrapper' | 'icon' | 'label', string>>} classNames
 * @property {(e: React.ChangeEvent<HTMLInputElement>) => void} onChange
 * @property {(isSelected: boolean) => void} onValueChange
 *
 * @typedef {object} CheckboxGroupProps
 * @property {ReactNode[] | ReactNode[]} children
 * @property {'vertical' | 'horizontal'} [orientation='vertical']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='primary']
 * @property {'xs' | 'sm' | 'md' | 'lg' | 'xl'} [size='md']
 * @property {'none' | 'base' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full'} [radius='md']
 * @property {string} name
 * @property {string} label
 * @property {string[]} value
 * @property {boolean} [lineThrough=false]
 * @property {string[]} defaultValue
 * @property {boolean} [isInvalid=false]
 * @property {'valid' | 'invalid'} validationState
 * @property {ReactNode} description
 * @property {ReactNode | ((v: ValidationResult) => ReactNode)} errorMessage
 * @property {(value: string[]) => ValidationError | true | null | undefined} validate
 * @property {'native' | 'aria'} [validationBehavior='native']
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [isRequired=false]
 * @property {boolean} isReadOnly
 * @property {boolean} [disableAnimation=false]
 * @property {Partial<Record<'base' | 'wrapper' | 'label', string>>} classNames
 * @property {(value: string[]) => void} onChange
 * @property {(value: T) => void} onValueChange
 */
