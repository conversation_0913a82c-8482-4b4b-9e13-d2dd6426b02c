{"name": "react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 3000 --open --host", "start:server": "json-server --watch ./src/data/db.json --port 3001", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@alireza-ab/persian-date": "^2.6.2", "@heroui/react": "^2.7.8", "@hookform/resolvers": "^5.0.1", "@internationalized/date": "^3.8.1", "@react-aria/i18n": "^3.12.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.77.0", "@tiptap/extension-bubble-menu": "^2.25.0", "@tiptap/extension-bullet-list": "^2.25.0", "@tiptap/extension-heading": "^2.25.0", "@tiptap/extension-image": "^2.25.0", "@tiptap/extension-link": "^2.25.0", "@tiptap/extension-list-item": "^2.25.0", "@tiptap/extension-ordered-list": "^2.25.0", "@tiptap/extension-strike": "^2.25.0", "@tiptap/extension-text-align": "^2.25.0", "@tiptap/extension-underline": "^2.25.0", "@tiptap/pm": "^2.25.0", "@tiptap/react": "^2.25.0", "@tiptap/starter-kit": "^2.25.0", "@uidotdev/usehooks": "^2.4.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "framer-motion": "^12.12.2", "iconsax-reactjs": "^0.0.8", "jalaliday": "^2.3.0", "js-cookie": "^3.0.5", "json-server": "1.0.0-beta.3", "nuqs": "^2.4.3", "prop-types": "^15.8.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.4", "react-player": "^2.16.0", "react-query-kit": "^3.3.1", "react-router": "^7.6.0", "recharts": "^2.15.3", "sass": "^1.89.0", "simplebar-react": "^3.3.1", "tailwind-merge": "^3.3.0", "up-fetch": "^2.1.2", "xlsx": "^0.18.5", "zod": "^3.25.28", "zustand": "^5.0.5"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "3", "vite": "^6.3.5"}}