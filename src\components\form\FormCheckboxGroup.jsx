import { CheckboxGroup } from "@heroui/react";
import PropTypes from "prop-types";
import { useController } from "react-hook-form";

/**
 * @param {FormCheckboxGroupProps} props
 */
const FormCheckboxGroup = (props) => {
  const { name, control, renderItem, items, children, checkboxGroupProps } =
    props;

  const {
    field: { onChange, value, ...field },
    fieldState,
  } = useController({ name, control });

  return (
    <CheckboxGroup
      {...field}
      value={value}
      isInvalid={!!fieldState.error}
      onValueChange={onChange}
      description={fieldState.error?.message}
      {...(({ children, ...rest }) => rest)(checkboxGroupProps || {})}
    >
      {items
        ? items.map((item) => renderItem?.(item))
        : children || checkboxGroupProps?.children}
    </CheckboxGroup>
  );
};

FormCheckboxGroup.propTypes = {
  name: PropTypes.string.isRequired,
  control: PropTypes.object.isRequired,
  items: PropTypes.array,
  renderItem: PropTypes.func,
  children: PropTypes.node,
  checkboxGroupProps: PropTypes.object,
};

export default FormCheckboxGroup;
