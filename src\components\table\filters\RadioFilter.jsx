import { Chip, cn, Radio, RadioGroup } from '@heroui/react';
import { useQueryState } from 'nuqs';
import PropTypes from 'prop-types';

const RadioFilter = (props) => {
  const {
    items,
    name,
    value,
    defaultItemLabel,
    setValue,
    className,
    ...otherProps
  } = props;
  const [filter, setFilter] = useQueryState(name);

  const handleValueChange = (e) => {
    if (setValue) {
      setValue(e);
    } else {
      setFilter(e);
    }
  };

  return (
    <RadioGroup
      aria-label={'radio-group'}
      value={value ?? filter}
      onValueChange={handleValueChange}
      classNames={{
        wrapper: cn('gap-3', className),
      }}
      {...otherProps}>
      <Radio value=''>{defaultItemLabel}</Radio>
      {items.map((item) => (
        <Radio key={item.value} value={item.value}>
          {item.label}
        </Radio>
      ))}
    </RadioGroup>
  );
};

RadioFilter.propTypes = {
  items: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
        .isRequired,
    }),
  ).isRequired,
  name: PropTypes.string.isRequired,
  label: PropTypes.node,
  orientation: PropTypes.oneOf(['vertical', 'horizontal']),
  color: PropTypes.oneOf([
    'primary',
    'secondary',
    'success',
    'warning',
    'danger',
  ]),
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  defaultItemLabel: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  setValue: PropTypes.func,
  className: PropTypes.string,
  historyProps: PropTypes.shape({
    className: PropTypes.string,
    chipClassName: PropTypes.string,
    chipSize: PropTypes.oneOf(['sm', 'md', 'lg']),
    chipRadius: PropTypes.oneOf(['none', 'sm', 'md', 'lg', 'full']),
    chipColor: PropTypes.oneOf([
      'primary',
      'secondary',
      'success',
      'warning',
      'danger',
    ]),
    chipVariant: PropTypes.oneOf([
      'solid',
      'bordered',
      'light',
      'flat',
      'dot',
      'faded',
      'shadow',
    ]),
  }),
};

const History = (props) => {
  const { name, items, label, value, setValue, historyProps } = props;
  const [filter, setFilter] = useQueryState(name);

  const filterItems = items?.filter((item) =>
    value ? value?.includes(item.value) : filter?.includes(item.value),
  );

  if (value ? !value?.length : !filter?.length) {
    return <></>;
  }

  return (
    <div
      className={cn(
        'flex w-max max-w-lg border-spacing-1 flex-wrap items-center gap-2 rounded-md border border-dashed border-foreground-100 px-1.5 py-2 font-medium',
        historyProps?.className,
      )}>
      <p className='text-sm'>{label}:</p>
      {filterItems.map((item) => (
        <Chip
          key={item.value}
          radius={historyProps?.chipRadius ?? 'sm'}
          size={historyProps?.chipSize ?? 'sm'}
          color={historyProps?.chipColor}
          variant={historyProps?.chipVariant}
          className={cn('p-1.5 text-sm', historyProps?.chipClassName)}
          onClose={() => {
            if (setValue) {
              setValue(
                value
                  .split(',')
                  .filter((f) => f !== item.value)
                  .toString(),
              );
            } else {
              setFilter(
                filter
                  .split(',')
                  .filter((f) => f !== item.value)
                  .toString(),
              );
            }
          }}>
          {item.label}
        </Chip>
      ))}
    </div>
  );
};

History.propTypes = {
  items: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
        .isRequired,
    }),
  ).isRequired,
  name: PropTypes.string.isRequired,
  label: PropTypes.node,
  value: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.arrayOf(PropTypes.string),
  ]),
  setValue: PropTypes.func,
  historyProps: PropTypes.shape({
    className: PropTypes.string,
    chipClassName: PropTypes.string,
    chipSize: PropTypes.oneOf(['sm', 'md', 'lg']),
    chipRadius: PropTypes.oneOf(['none', 'sm', 'md', 'lg', 'full']),
    chipColor: PropTypes.oneOf([
      'primary',
      'secondary',
      'success',
      'warning',
      'danger',
    ]),
    chipVariant: PropTypes.oneOf([
      'solid',
      'bordered',
      'light',
      'flat',
      'dot',
      'faded',
      'shadow',
    ]),
  }),
};

RadioFilter.History = History;
History.displayName = 'radio-filter-history';
RadioFilter.showInDrawer = true;

export default RadioFilter;
