import PropTypes from "prop-types";
import useBreadcrumbs from "../../hooks/useBreadcrums";
import PageTitleBreadcrumbs from "./PageTitleBreadcrumbs";
const PageWrapper = ({ children, headerContent, hasTitle = true }) => {
  const breadcrumbs = useBreadcrumbs();

  return (
    <section>
      <div className="flex flex-col gap-8 px-4 py-8 sm:px-6 lg:px-8">
        <div className="flex flex-wrap items-start justify-between">
          <PageTitleBreadcrumbs breadcrumbs={breadcrumbs} hasTitle={hasTitle} />
          <div className="flex flex-wrap justify-end">{headerContent}</div>
        </div>
        {children}
      </div>
    </section>
  );
};

PageWrapper.propTypes = {
  hasTitle: PropTypes.bool,
  headerContent: PropTypes.node,
  children: PropTypes.node.isRequired,
};

export default PageWrapper;
