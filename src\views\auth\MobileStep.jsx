import { Button, addToast } from "@heroui/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { Call } from "iconsax-reactjs";
import PropTypes from "prop-types";
import { useForm } from "react-hook-form";
import { z } from "zod";
import api from "../../api";
import FormInput from "../../components/form/FormInput";

const schema = z.object({
  mobile: z
    .string()
    .regex(/^\d+$/, "لطفا فقط عدد وارد کنید")
    .min(11, "شماره تلفن باید حداقل ۱۱ رقم باشد")
    .max(11, "شماره تلفن باید دقیقاً ۱۱ رقم باشد")
    .regex(/^09[0-9]{9}$/, "شماره تلفن معتبر نیست"),
  // otp: z.string().length(4, 'کد تایید باید ۴ رقم باشد'),
});

const MobileStep = ({ onSuccess }) => {
  // Form
  const { control, handleSubmit, getValues, reset } = useForm({
    mode: "onChange",
    resolver: zodResolver(schema),
    defaultValues: {
      mobile: "",
    },
  });

  // Login Mutation
  const { mutate, isPending } = api.Auth.login.useMutation({
    onSuccess: (data) => {
      onSuccess({
        ...data,
        mobile: getValues("mobile"),
      });
      reset();
    },
    onError: (error) => {
      if (error?.response?.status === 400) {
        addToast({
          title: "شما به این صفحه دسترسی ندارید",
          color: "danger",
        });
      } else {
        addToast({
          title: "مشکلی پیش آمده است",
          description: "لطفا مجددا تلاش کنید",
          color: "danger",
        });
      }
    },
  });

  return (
    <>
      <p className="text-center font-medium">
        لطفا شماره موبایل خود را وارد کنید تا کد تایید برای شما ارسال شود
      </p>

      <FormInput
        control={control}
        name="mobile"
        inputProps={{
          onKeyDown: (e) => {
            if (e.key === "Enter") {
              e.preventDefault();
              handleSubmit(mutate)();
            }
          },
          classNames: {
            base: "mt-6",
            inputWrapper:
              "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
            input: "text-sm",
          },
          radius: "full",
          size: "lg",
          placeholder: "شماره موبایل",
          startContent: <Call className="size-6 text-primary" />,
        }}
      />

      <Button
        onPress={handleSubmit(mutate)}
        fullWidth
        isLoading={isPending}
        type="submit"
        color="primary"
        className="mt-6 font-semibold"
        size="lg"
        radius="full"
      >
        ارسال
      </Button>
    </>
  );
};

export default MobileStep;

MobileStep.propTypes = {
  onSuccess: PropTypes.func,
};
