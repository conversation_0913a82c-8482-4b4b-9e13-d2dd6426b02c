import { Textarea, cn } from "@heroui/react";
import PropTypes from "prop-types";
import { useController } from "react-hook-form";
import convertInvalidCharacter from "../../utils/convertInvalidCharacter";
/**
 * @param {FormTextareaProps} props
 */
const FormTextarea = ({ control, name, rules, textareaProps = {} }) => {
  const {
    field: { onChange, value, ...field },
    fieldState,
  } = useController({
    name,
    control,
    rules: rules,
  });

  const handleInvalidCharacter = (val) => {
    const newValue = convertInvalidCharacter(val);
    if (newValue !== value) {
      onChange(newValue);
    }
  };

  return (
    <Textarea
      {...field}
      onValueChange={handleInvalidCharacter}
      value={convertInvalidCharacter(value ?? "")}
      isInvalid={!!fieldState.error}
      errorMessage={fieldState.error?.message}
      classNames={{
        inputWrapper: cn(
          "hover:bg-background-50 dark:hover:bg-background-50",
          { "bg-background-100": !fieldState.error },
          textareaProps?.classNames?.inputWrapper,
        ),
        ...(({ inputWrapper, ...rest }) => rest)(
          textareaProps?.classNames || {},
        ),
      }}
      {...textareaProps}
    />
  );
};

FormTextarea.propTypes = {
  name: PropTypes.string.isRequired,
  control: PropTypes.object.isRequired,
  rules: PropTypes.object,
  textareaProps: PropTypes.object,
};

export default FormTextarea;
