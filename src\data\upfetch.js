import { up } from 'up-fetch';
import Cookies from 'js-cookie';

const base_url = import.meta.env.VITE_BASE_API;

const TOKEN_KEY = import.meta.env.VITE_TOKEN_KEY;

const uf = up(fetch, () => ({
  baseUrl: base_url,
  onRequest: (config) => {
    if (Cookies.get(TOKEN_KEY)) {
      config.headers.set('Authorization', `Bearer ${Cookies.get(TOKEN_KEY)}`);
      config.headers.set('Accept', 'application/json, text/plain, */*');
    }
    return config;
  },
}));

export default uf;
