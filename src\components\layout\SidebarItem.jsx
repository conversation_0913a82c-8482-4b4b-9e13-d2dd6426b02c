import { cn } from "@heroui/react";
import { motion } from "framer-motion";
import PropTypes from "prop-types";
import { useMemo } from "react";
import { Link, useLocation } from "react-router";
import useSidebarStore from "../../stores/useSidebarStore";
import Icon from "../icon/Icon";

const SidebarItem = ({ label, prefix, action, isMini }) => {
  const { isSidebarOpen } = useSidebarStore();
  const location = useLocation();

  const isActive = useMemo(() => {
    if (typeof action !== "string") return false;
    if (action === "/") {
      return location.pathname === action;
    }
    return location.pathname.startsWith(action);
  }, [location, action]);

  const Component = action && typeof action === "string" ? Link : "div";

  const IconComp = prefix && typeof prefix === "string" ? Icon : prefix;

  const props =
    action && typeof action === "string"
      ? { to: action }
      : {
          onClick: () => {
            action?.();
          },
        };

  return (
    <Component
      {...props}
      className={cn(
        "group relative flex cursor-pointer items-center gap-2 py-5 ps-8 transition-all duration-300",
        {
          "bg-primary-50": isActive,
          "bg-transparent": !isActive,
          "flex-col ps-0": isMini && !isSidebarOpen,
        },
      )}
    >
      <IconComp
        {...(prefix && typeof prefix === "string" ? { name: prefix } : {})}
        className={cn(
          "size-[22px] text-foreground-400 transition-all duration-300",
          {
            "text-primary": isActive,
            "group-hover:text-foreground-900": !isActive,
          },
        )}
      />
      <span
        className={cn(
          "line-clamp-1 text-start text-base font-medium text-foreground-400 transition-all duration-300",
          {
            "text-foreground": isActive,
            "line-clamp-2 px-1 text-center text-sm": isMini && !isSidebarOpen,
            "group-hover:text-foreground-900": !isActive,
          },
        )}
      >
        {label}
      </span>
      {isActive && (
        <motion.div
          initial={{ x: 30, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          exit={{
            x: 30,
            opacity: 0,
            transition: {
              duration: 0.2,
            },
          }}
          transition={{ duration: 0.4 }}
          className={cn("absolute inset-y-0 right-0 w-2.5 bg-primary", {
            hidden: isMini && !isSidebarOpen,
          })}
        />
      )}
    </Component>
  );
};

SidebarItem.propTypes = {
  label: PropTypes.string.isRequired,
  prefix: PropTypes.oneOfType([PropTypes.string, PropTypes.element]).isRequired,
  action: PropTypes.oneOfType([PropTypes.func, PropTypes.string]).isRequired,
  isMini: PropTypes.bool,
};
export default SidebarItem;
