import { BreadcrumbItem, Breadcrumbs } from "@heroui/react";
import { Home } from "iconsax-reactjs";
import PropTypes from "prop-types";
import { Link } from "react-router";

const PageTitleBreadcrumbs = ({ breadcrumbs, hasTitle = true }) => {
  return (
    <div className="flex flex-col gap-3">
      <h2 className="line-clamp-1 text-2xl font-semibold text-foreground">
        {hasTitle && breadcrumbs.at(-1)?.label}
      </h2>
      {breadcrumbs.length > 1 && (
        <Breadcrumbs>
          {breadcrumbs.map((item, index) => (
            <BreadcrumbItem key={item.link}>
              {breadcrumbs.length - 1 === index ? (
                item.label
              ) : item.link === "/" ? (
                <Link to={item.link}>
                  <Home size={18} className="text-foreground-500" />
                </Link>
              ) : (
                <Link className="text-foreground-500" to={item.link}>
                  {item.label}
                </Link>
              )}
            </BreadcrumbItem>
          ))}
        </Breadcrumbs>
      )}
    </div>
  );
};

PageTitleBreadcrumbs.propTypes = {
  hasTitle: PropTypes.bool,
  breadcrumbs: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      link: PropTypes.string.isRequired,
    }),
  ).isRequired,
};

export default PageTitleBreadcrumbs;
