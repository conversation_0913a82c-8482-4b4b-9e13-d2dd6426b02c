import { BubbleMenu } from "@tiptap/react";
import PropTypes from "prop-types";
import MenuBar from "./MenuBar";

const BubbleBar = ({ editor, isVisible, enabledButtons }) => {
  return (
    <BubbleMenu
      tippyOptions={{ zIndex: 10 }}
      className={isVisible ? "" : "hidden"}
      editor={editor}
    >
      <MenuBar
        editor={editor}
        enabledButtons={enabledButtons}
        className="w-max max-w-full rounded-medium bg-background-100 p-2"
      />
    </BubbleMenu>
  );
};

BubbleBar.propTypes = {
  editor: PropTypes.object.isRequired,
  isVisible: PropTypes.bool.isRequired,
  enabledButtons: PropTypes.object,
};

export default BubbleBar;
