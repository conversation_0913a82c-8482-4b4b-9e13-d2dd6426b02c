{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": ["node_modules", "public", "dist"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "a11y": {"all": false}, "correctness": {"useExhaustiveDependencies": "warn"}}}, "javascript": {"formatter": {"trailingCommas": "all"}}}