import { Button, cn } from '@heroui/react';
import { Trash } from 'iconsax-reactjs';
import { parseAsString, useQueryStates } from 'nuqs';
import PropTypes from 'prop-types';
import { useMemo } from 'react';

const FilterHistory = ({
  fundedItems = 0,
  children: _children,
  className,
  showChildren = true,
  value,
  setValue,
}) => {
  const children = Array.isArray(_children) ? _children : [_children];

  const [params, setParams] = useQueryStates(
    children.reduce(
      (a, child) => ({ ...a, [child.props?.name]: parseAsString }),
      {},
    ),
  );

  const hasActiveFilters = useMemo(() => {
    return !!Object.values(value ?? params).filter((x) => !!x).length;
  }, [params, value]);

  const handleRemoveFilter = (key) => {
    return (value) => {
      setValue((prev) => ({ ...prev, [key]: value }));
    };
  };

  return (
    <>
      {showChildren && (
        <div
          className={cn(
            'flex flex-wrap items-center gap-x-3 gap-y-4',
            className,
          )}>
          {_children}
        </div>
      )}
      {hasActiveFilters && (
        <div className='flex flex-col items-start gap-4'>
          <span className='flex gap-2 text-lg font-semibold'>
            {fundedItems}
            <small className='font-medium'>آیتم پیدا شد</small>
          </span>
          <div className='flex flex-wrap items-center gap-2'>
            {children.map((item, index) => {
              const Comp = item.type?.History;
              return (
                <Comp
                  key={index}
                  {...item.props}
                  value={value ? value[item?.props?.name] : undefined}
                  setValue={
                    setValue ? handleRemoveFilter(item?.props?.name) : undefined
                  }
                />
              );
            })}
            <Button
              size='sm'
              onPress={() => {
                if (setValue) {
                  setValue({});
                } else {
                  setParams(null);
                }
              }}
              className='bg-transparent font-semibold text-danger hover:bg-danger-100'
              endContent={<Trash className='size-5' />}>
              حذف همه
            </Button>
          </div>
        </div>
      )}
    </>
  );
};

FilterHistory.propTypes = {
  fundedItems: PropTypes.number,
  children: PropTypes.array,
  className: PropTypes.string,
  showChildren: PropTypes.bool,
  value: PropTypes.object,
  setValue: PropTypes.func,
};

export default FilterHistory;
