const convertInvalidCharacter = (text) => {
  const numbers = [
    '۰',
    '۱',
    '۲',
    '۳',
    '۴',
    '۵',
    '۶',
    '۷',
    '۸',
    '۹',
    '٠',
    '١',
    '٢',
    '٣',
    '٤',
    '٥',
    '٦',
    '٧',
    '٨',
    '٩',
  ];
  const output = [];
  const chars = {
    ك: 'ک',
    دِ: 'د',
    بِ: 'ب',
    زِ: 'ز',
    ذِ: 'ذ',
    شِ: 'ش',
    سِ: 'س',
    ى: 'ی',
    ي: 'ی',
  };
  text.split('').forEach((char) => {
    output.push(
      numbers.includes(char)
        ? numbers.indexOf(char) % 10
        : char in chars
          ? chars[char]
          : char,
    );
  });
  return output.join('');
};

export default convertInvalidCharacter;
