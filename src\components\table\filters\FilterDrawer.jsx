import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from '@heroui/react';
import { Refresh } from 'iconsax-reactjs';
import { ArrowRight } from 'iconsax-reactjs';
import { parseAsString, useQueryStates } from 'nuqs';
import PropTypes from 'prop-types';
import { useEffect } from 'react';
import { useCallback } from 'react';
import { useState } from 'react';
import SimpleBar from 'simplebar-react';
import FilterHistory from './FilterHistory';

const FilterDrawer = ({
  isOpen,
  onOpenChange,
  onClose,
  children: _children,
  placement = 'left',
  size = 'xs',
  radius = 'none',
  value,
  setValue,
  hasHistory = true,
  className,
}) => {
  const children = Array.isArray(_children) ? _children : [_children];

  const [params, setParams] = useQueryStates(
    children.reduce(
      (a, child) => ({ ...a, [child.props?.name]: parseAsString }),
      {},
    ),
  );

  const [filters, setFilters] = useState({});

  const handleFilter = useCallback((key) => {
    return (value) => {
      setFilters((prev) => ({ ...prev, [key]: value }));
    };
  }, []);

  const handleRefresh = () => {
    if (setValue) {
      setValue({});
    } else {
      setParams(null);
    }
  };

  const handleApply = () => {
    if (setValue) {
      setValue(filters);
    } else {
      setParams(filters);
    }
    onClose();
  };

  useEffect(() => {
    if (isOpen) {
      if (value) {
        setFilters(value);
      } else {
        setFilters(params);
      }
    }
  }, [isOpen, params, value]);

  return (
    <>
      {children.map((child) => {
        if (!child.type.showInDrawer) {
          return child;
        }
      })}

      {hasHistory && (
        <FilterHistory value={value} setValue={setValue} showChildren={false}>
          {children}
        </FilterHistory>
      )}

      <Drawer
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        placement={placement}
        size={size}
        radius={radius}
        className={className}
        hideCloseButton>
        <DrawerContent>
          <DrawerHeader className='flex items-center gap-2 px-4 pb-2 pt-4'>
            <Button
              radius='full'
              variant='light'
              className='size-9 min-h-2 min-w-2 text-foreground-500 hover:text-foreground'
              isIconOnly
              onPress={onClose}>
              <ArrowRight className='size-5 cursor-pointer' />
            </Button>

            <p className='me-auto text-lg font-semibold'>فیلتر ها</p>
            <Tooltip
              placement='right'
              showArrow={true}
              content='بازنشانی فیلتر ها'>
              <Button
                radius='full'
                variant='light'
                className='size-9 min-h-2 min-w-2 text-foreground-500 hover:text-foreground'
                isIconOnly
                onPress={onClose}>
                <Refresh onClick={handleRefresh} className='size-5' />
              </Button>
            </Tooltip>
          </DrawerHeader>
          <DrawerBody className='overflow-y-hidden px-0'>
            <SimpleBar className='h-full'>
              <div className='mt-3 flex flex-col gap-6 px-4'>
                {children.map((child) => {
                  if (child.type.showInDrawer) {
                    return (
                      <child.type
                        key={child.props?.name}
                        {...child.props}
                        value={filters?.[child.props?.name] ?? ''}
                        setValue={handleFilter(child.props?.name)}
                      />
                    );
                  }
                })}
              </div>
            </SimpleBar>
          </DrawerBody>
          <DrawerFooter>
            <div className='flex w-full'>
              <Button
                onPress={handleApply}
                variant='shadow'
                color='primary'
                className='grow'>
                اعمال فیلتر
              </Button>
            </div>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </>
  );
};

FilterDrawer.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  className: PropTypes.string,
  onOpenChange: PropTypes.func.isRequired,
  onClose: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
  value: PropTypes.object,
  setValue: PropTypes.func,
  hasHistory: PropTypes.bool,
  radius: PropTypes.oneOf(['none', 'sm', 'md', 'lg']),
  placement: PropTypes.oneOf(['left', 'right', 'top', 'bottom']),
  size: PropTypes.oneOf([
    'xs',
    'sm',
    'md',
    'lg',
    'xl',
    '2xl',
    '3xl',
    '4xl',
    '5xl',
    'full',
  ]),
};

export default FilterDrawer;
