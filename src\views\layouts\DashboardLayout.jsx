import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  cn,
  useDisclosure,
} from "@heroui/react";

import { useMediaQuery } from "@uidotdev/usehooks";
import { ArrowLeft2 } from "iconsax-reactjs";
import Cookies from "js-cookie";
import { useState } from "react";
import { useEffect } from "react";
import { Outlet, useMatches, useNavigate } from "react-router";
import AboutModal from "../../components/layout/AboutModal";
import Header from "../../components/layout/Header";
import Sidebar from "../../components/layout/Sidebar";
import SidebarGroup from "../../components/layout/SidebarGroup";
import SidebarHeader from "../../components/layout/SidebarHeader";
import SidebarItem from "../../components/layout/SidebarItem";
import SidebarMenu from "../../components/layout/SidebarMenu";
import menus from "../../data/menus.jsx";
import useSidebarStore from "../../stores/useSidebarStore";

const TOKEN_KEY = import.meta.env.VITE_TOKEN_KEY;

const DashboardLayout = () => {
  const navigate = useNavigate();

  const matches = useMatches();
  document.title =
    document.title = `باران - ${matches?.at(-1)?.handle?.title ?? " "}`;

  const { isSidebarOpen, toggleSidebar } = useSidebarStore();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const isSmallScreen = useMediaQuery("only screen and (max-width : 1024px)");

  useEffect(() => {
    if (isSidebarOpen && isSmallScreen) {
      setIsDrawerOpen(false);
    }
  }, [isSidebarOpen, isSmallScreen]);

  useEffect(() => {
    if (!isSmallScreen) {
      setIsDrawerOpen(false);
    } else {
      useSidebarStore.setState({ isSidebarOpen: true });
    }
  }, [isSmallScreen]);

  const toggleDrawer = () => {
    setIsDrawerOpen(!isDrawerOpen);
  };

  // Logout Modal Disclosure
  const { isOpen, onOpen, onOpenChange } = useDisclosure();

  return (
    <>
      <section className="flex h-dvh overflow-hidden bg-background-100/80">
        {/* Sidebar */}
        {isSmallScreen ? (
          <Drawer
            isOpen={isDrawerOpen}
            onClose={toggleDrawer}
            placement="right"
            className="w-[240px]"
            hideCloseButton
          >
            <DrawerContent>
              <Sidebar>
                <SidebarHeader />
                <SidebarMenu>
                  {menus.map((menu, index) => (
                    <>
                      {index > 0 && (
                        <span className="my-3 h-px bg-gradient-to-tr from-transparent via-foreground-100 to-transparent" />
                      )}
                      <SidebarGroup key={`group-drawer-${menu.id}`}>
                        {menu.children.map((child) => (
                          <SidebarItem
                            isMini={true}
                            key={`item-drawer-${child.id}`}
                            {...child}
                          />
                        ))}
                      </SidebarGroup>
                    </>
                  ))}
                  <span className="my-3 h-px bg-gradient-to-tr from-transparent via-foreground-100 to-transparent" />
                  <SidebarItem
                    isMini={true}
                    key={"item-sidebar-"}
                    action={() => {
                      onOpen();
                    }}
                    prefix="logout"
                    label="خروج"
                  />
                </SidebarMenu>
              </Sidebar>
            </DrawerContent>
          </Drawer>
        ) : (
          <Sidebar>
            <SidebarHeader />
            <SidebarMenu>
              {menus.map((menu, index) => (
                <>
                  {index > 0 && (
                    <span className="my-3 h-px bg-gradient-to-tr from-transparent via-foreground-100 to-transparent" />
                  )}
                  <SidebarGroup key={`group-sidebar-${menu.id}`}>
                    {menu.children.map((child) => (
                      <SidebarItem
                        isMini={true}
                        key={`item-sidebar-${child.id}`}
                        {...child}
                      />
                    ))}
                  </SidebarGroup>
                </>
              ))}
              <span className="my-3 h-px bg-gradient-to-tr from-transparent via-foreground-100 to-transparent" />
              <SidebarItem
                isMini={true}
                key={"item-sidebar-"}
                action={() => {
                  onOpen();
                }}
                prefix="logout"
                label="خروج"
              />
            </SidebarMenu>
            <Button
              className="absolute -left-4 bottom-16 h-8 min-h-0 w-8 min-w-0 border-1 border-foreground-100/70 bg-background"
              radius="full"
              isIconOnly
              onPress={isSmallScreen ? toggleDrawer : toggleSidebar}
              variant="bordered"
            >
              <ArrowLeft2
                className={cn("size-4 transition-all duration-300", {
                  "rotate-180": isSidebarOpen,
                })}
              />
            </Button>
          </Sidebar>
        )}
        {/* Logout Modal */}
        <Modal
          placement="center"
          size="lg"
          isOpen={isOpen}
          className="mx-4"
          onOpenChange={onOpenChange}
        >
          <ModalContent className="rounded-small px-2 pb-5 pt-3 sm:px-4 md:px-6 md:pb-6 md:pt-4 lg:px-8 lg:pb-8">
            {(onClose) => (
              <>
                <ModalHeader className="flex flex-col gap-1" />
                <ModalBody className="gap flex flex-col items-center">
                  <p className="text-lg font-semibold text-danger">خروج</p>

                  <p className="mb-4 mt-3 text-center text-lg font-semibold">
                    آیا از اینکه میخواهید خارج شوید مطمئن هستید
                  </p>
                </ModalBody>
                <ModalFooter className="justify-center">
                  <Button
                    variant="bordered"
                    className="h-11 min-h-0 w-full max-w-36 text-base font-medium lg:h-12"
                    radius="full"
                    size="lg"
                    onPress={onClose}
                  >
                    بستن
                  </Button>
                  <Button
                    className="h-11 min-h-0 w-full max-w-36 text-base font-medium lg:h-12"
                    color="danger"
                    radius="full"
                    onPress={() => {
                      Cookies.remove(TOKEN_KEY);
                      navigate("/login", { replace: true });
                    }}
                  >
                    بله
                  </Button>
                </ModalFooter>
              </>
            )}
          </ModalContent>
        </Modal>
        {/* About Modal */}
        <AboutModal />
        {/* Main content area */}
        <div className="flex flex-1 flex-col">
          {/* Header */}
          <Header toggleDrawer={isSmallScreen ? toggleDrawer : toggleSidebar} />
          {/* Main content */}
          <div className="custom-scrollbar flex-1 overflow-y-auto">
            <Outlet />
          </div>
        </div>
      </section>
    </>
  );
};

export default DashboardLayout;
